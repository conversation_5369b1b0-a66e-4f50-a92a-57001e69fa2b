'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON><PERSON>, Loader2 } from 'lucide-react'
import { useEditorStore } from '@/store/editorStore'
import { AIGenerationRequest } from '@/types'

interface AIGenerationFormProps {
  onGenerate: (request: AIGenerationRequest) => Promise<void>
}

export function AIGenerationForm({ onGenerate }: AIGenerationFormProps) {
  const [title, setTitle] = useState('')
  const [abstract, setAbstract] = useState('')
  const [field, setField] = useState('')
  const [style, setStyle] = useState<'modern' | 'academic' | 'minimal' | 'colorful'>('modern')
  const [elements, setElements] = useState<string[]>([])
  const [isGenerating, setIsGenerating] = useState(false)

  const { isLoading } = useEditorStore()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!title.trim() || !abstract.trim()) {
      return
    }

    setIsGenerating(true)
    
    try {
      await onGenerate({
        title: title.trim(),
        abstract: abstract.trim(),
        field: field.trim() || 'General Science',
        style,
        elements
      })
    } catch (error) {
      console.error('Generation failed:', error)
    } finally {
      setIsGenerating(false)
    }
  }

  const handleElementToggle = (element: string) => {
    setElements(prev => 
      prev.includes(element) 
        ? prev.filter(e => e !== element)
        : [...prev, element]
    )
  }

  const suggestedElements = [
    'Molecules', 'Cells', 'DNA', 'Proteins', 'Graphs', 'Charts',
    'Arrows', 'Process Flow', 'Timeline', 'Comparison', 'Results',
    'Methods', 'Hypothesis', 'Conclusion', 'Data Visualization'
  ]

  const styles = [
    { value: 'modern', label: 'Modern', description: 'Clean, contemporary design' },
    { value: 'academic', label: 'Academic', description: 'Traditional scientific style' },
    { value: 'minimal', label: 'Minimal', description: 'Simple, focused layout' },
    { value: 'colorful', label: 'Colorful', description: 'Vibrant, engaging visuals' }
  ]

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Sparkles className="h-5 w-5 text-blue-500" />
          AI-Powered Generation
        </CardTitle>
        <CardDescription>
          Enter your manuscript details to generate a professional graphical abstract
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <label htmlFor="title" className="text-sm font-medium">
              Manuscript Title *
            </label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter your manuscript title..."
              required
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="abstract" className="text-sm font-medium">
              Abstract *
            </label>
            <Textarea
              id="abstract"
              value={abstract}
              onChange={(e) => setAbstract(e.target.value)}
              placeholder="Paste your abstract here..."
              className="min-h-[120px]"
              required
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="field" className="text-sm font-medium">
              Research Field
            </label>
            <Input
              id="field"
              value={field}
              onChange={(e) => setField(e.target.value)}
              placeholder="e.g., Biology, Chemistry, Physics..."
            />
          </div>

          <div className="space-y-3">
            <label className="text-sm font-medium">Style Preference</label>
            <div className="grid grid-cols-2 gap-3">
              {styles.map((styleOption) => (
                <button
                  key={styleOption.value}
                  type="button"
                  onClick={() => setStyle(styleOption.value as any)}
                  className={`p-3 rounded-lg border text-left transition-colors ${
                    style === styleOption.value
                      ? 'border-blue-500 bg-blue-50 text-blue-900'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="font-medium">{styleOption.label}</div>
                  <div className="text-xs text-gray-500">{styleOption.description}</div>
                </button>
              ))}
            </div>
          </div>

          <div className="space-y-3">
            <label className="text-sm font-medium">
              Suggested Elements (Optional)
            </label>
            <div className="flex flex-wrap gap-2">
              {suggestedElements.map((element) => (
                <button
                  key={element}
                  type="button"
                  onClick={() => handleElementToggle(element)}
                  className={`px-3 py-1 rounded-full text-sm transition-colors ${
                    elements.includes(element)
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {element}
                </button>
              ))}
            </div>
          </div>

          <Button
            type="submit"
            className="w-full"
            disabled={isGenerating || isLoading || !title.trim() || !abstract.trim()}
          >
            {isGenerating ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Sparkles className="h-4 w-4 mr-2" />
                Generate Graphical Abstract
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
