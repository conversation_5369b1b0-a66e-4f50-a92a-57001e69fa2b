{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/ubuntu/graphical_abstract/src/components/ui/slot.tsx"], "sourcesContent": ["import * as React from \"react\"\n\ninterface SlotProps extends React.HTMLAttributes<HTMLElement> {\n  children?: React.ReactNode\n}\n\nconst Slot = React.forwardRef<HTMLElement, SlotProps>(\n  ({ children, ...props }, ref) => {\n    if (React.isValidElement(children)) {\n      return React.cloneElement(children, {\n        ...props,\n        ...children.props,\n        ref: ref as any,\n      })\n    }\n\n    if (React.Children.count(children) > 1) {\n      React.Children.only(null)\n    }\n\n    return null\n  }\n)\n\nSlot.displayName = \"Slot\"\n\nexport { Slot }\n"], "names": [], "mappings": ";;;AAAA;;AAMA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC1B,CAAC,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACvB,kBAAI,CAAA,GAAA,6JAAA,CAAA,iBAAoB,AAAD,EAAE,WAAW;QAClC,qBAAO,CAAA,GAAA,6JAAA,CAAA,eAAkB,AAAD,EAAE,UAAU;YAClC,GAAG,KAAK;YACR,GAAG,SAAS,KAAK;YACjB,KAAK;QACP;IACF;IAEA,IAAI,6JAAA,CAAA,WAAc,CAAC,KAAK,CAAC,YAAY,GAAG;QACtC,6JAAA,CAAA,WAAc,CAAC,IAAI,CAAC;IACtB;IAEA,OAAO;AACT;;AAGF,KAAK,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/ubuntu/graphical_abstract/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport function downloadFile(blob: Blob, filename: string) {\n  const url = URL.createObjectURL(blob)\n  const a = document.createElement('a')\n  a.href = url\n  a.download = filename\n  document.body.appendChild(a)\n  a.click()\n  document.body.removeChild(a)\n  URL.revokeObjectURL(url)\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\nexport function validateImageFile(file: File): boolean {\n  const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']\n  return validTypes.includes(file.type)\n}\n\nexport function resizeImage(file: File, maxWidth: number, maxHeight: number): Promise<Blob> {\n  return new Promise((resolve) => {\n    const canvas = document.createElement('canvas')\n    const ctx = canvas.getContext('2d')!\n    const img = new Image()\n    \n    img.onload = () => {\n      const { width, height } = img\n      const ratio = Math.min(maxWidth / width, maxHeight / height)\n      \n      canvas.width = width * ratio\n      canvas.height = height * ratio\n      \n      ctx.drawImage(img, 0, 0, canvas.width, canvas.height)\n      canvas.toBlob(resolve!, 'image/png')\n    }\n    \n    img.src = URL.createObjectURL(file)\n  })\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,aAAa,IAAU,EAAE,QAAgB;IACvD,MAAM,MAAM,IAAI,eAAe,CAAC;IAChC,MAAM,IAAI,SAAS,aAAa,CAAC;IACjC,EAAE,IAAI,GAAG;IACT,EAAE,QAAQ,GAAG;IACb,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,EAAE,KAAK;IACP,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,IAAI,eAAe,CAAC;AACtB;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IACxB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAEO,SAAS,kBAAkB,IAAU;IAC1C,MAAM,aAAa;QAAC;QAAc;QAAa;QAAa;QAAc;KAAgB;IAC1F,OAAO,WAAW,QAAQ,CAAC,KAAK,IAAI;AACtC;AAEO,SAAS,YAAY,IAAU,EAAE,QAAgB,EAAE,SAAiB;IACzE,OAAO,IAAI,QAAQ,CAAC;QAClB,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,MAAM,MAAM,OAAO,UAAU,CAAC;QAC9B,MAAM,MAAM,IAAI;QAEhB,IAAI,MAAM,GAAG;YACX,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;YAC1B,MAAM,QAAQ,KAAK,GAAG,CAAC,WAAW,OAAO,YAAY;YAErD,OAAO,KAAK,GAAG,QAAQ;YACvB,OAAO,MAAM,GAAG,SAAS;YAEzB,IAAI,SAAS,CAAC,KAAK,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;YACpD,OAAO,MAAM,CAAC,SAAU;QAC1B;QAEA,IAAI,GAAG,GAAG,IAAI,eAAe,CAAC;IAChC;AACF", "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/ubuntu/graphical_abstract/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"./slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mIAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/ubuntu/graphical_abstract/src/store/editorStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { devtools } from 'zustand/middleware'\nimport { EditorState, CanvasElement, GraphicalAbstract } from '@/types'\nimport { generateId } from '@/lib/utils'\n\ninterface EditorStore extends EditorState {\n  // Actions\n  setCanvas: (canvas: Partial<GraphicalAbstract['canvas']>) => void\n  addElement: (element: Omit<CanvasElement, 'id'>) => void\n  updateElement: (id: string, updates: Partial<CanvasElement>) => void\n  deleteElement: (id: string) => void\n  deleteElements: (ids: string[]) => void\n  selectElement: (id: string) => void\n  selectElements: (ids: string[]) => void\n  deselectAll: () => void\n  duplicateElement: (id: string) => void\n  duplicateElements: (ids: string[]) => void\n  moveElement: (id: string, position: { x: number; y: number }) => void\n  resizeElement: (id: string, size: { width: number; height: number }) => void\n  rotateElement: (id: string, rotation: number) => void\n  bringToFront: (id: string) => void\n  sendToBack: (id: string) => void\n  copyElements: (ids: string[]) => void\n  pasteElements: () => void\n  undo: () => void\n  redo: () => void\n  setZoom: (zoom: number) => void\n  setPan: (pan: { x: number; y: number }) => void\n  setTool: (tool: EditorState['tool']) => void\n  setLoading: (loading: boolean) => void\n  setError: (error: string | null) => void\n  reset: () => void\n  loadProject: (project: GraphicalAbstract) => void\n}\n\nconst initialState: EditorState = {\n  canvas: {\n    width: 1200,\n    height: 800,\n    backgroundColor: '#ffffff'\n  },\n  elements: [],\n  selectedElementIds: [],\n  clipboard: [],\n  history: {\n    past: [],\n    present: [],\n    future: []\n  },\n  zoom: 1,\n  pan: { x: 0, y: 0 },\n  tool: 'select',\n  isLoading: false,\n  error: null\n}\n\nexport const useEditorStore = create<EditorStore>()(\n  devtools(\n    (set, get) => ({\n      ...initialState,\n\n      setCanvas: (canvas) =>\n        set((state) => ({\n          canvas: { ...state.canvas, ...canvas }\n        })),\n\n      addElement: (element) =>\n        set((state) => {\n          const newElement: CanvasElement = {\n            ...element,\n            id: generateId(),\n            zIndex: Math.max(...state.elements.map(e => e.zIndex), 0) + 1\n          }\n          return {\n            elements: [...state.elements, newElement],\n            history: {\n              past: [...state.history.past, state.history.present],\n              present: [...state.elements, newElement],\n              future: []\n            }\n          }\n        }),\n\n      updateElement: (id, updates) =>\n        set((state) => {\n          const updatedElements = state.elements.map(element =>\n            element.id === id ? { ...element, ...updates } : element\n          )\n          return {\n            elements: updatedElements,\n            history: {\n              past: [...state.history.past, state.history.present],\n              present: updatedElements,\n              future: []\n            }\n          }\n        }),\n\n      deleteElement: (id) =>\n        set((state) => {\n          const filteredElements = state.elements.filter(element => element.id !== id)\n          return {\n            elements: filteredElements,\n            selectedElementIds: state.selectedElementIds.filter(selectedId => selectedId !== id),\n            history: {\n              past: [...state.history.past, state.history.present],\n              present: filteredElements,\n              future: []\n            }\n          }\n        }),\n\n      deleteElements: (ids) =>\n        set((state) => {\n          const filteredElements = state.elements.filter(element => !ids.includes(element.id))\n          return {\n            elements: filteredElements,\n            selectedElementIds: state.selectedElementIds.filter(selectedId => !ids.includes(selectedId)),\n            history: {\n              past: [...state.history.past, state.history.present],\n              present: filteredElements,\n              future: []\n            }\n          }\n        }),\n\n      selectElement: (id) =>\n        set({ selectedElementIds: [id] }),\n\n      selectElements: (ids) =>\n        set({ selectedElementIds: ids }),\n\n      deselectAll: () =>\n        set({ selectedElementIds: [] }),\n\n      duplicateElement: (id) =>\n        set((state) => {\n          const element = state.elements.find(e => e.id === id)\n          if (!element) return state\n\n          const duplicatedElement: CanvasElement = {\n            ...element,\n            id: generateId(),\n            position: {\n              x: element.position.x + 20,\n              y: element.position.y + 20\n            },\n            zIndex: Math.max(...state.elements.map(e => e.zIndex), 0) + 1\n          }\n\n          return {\n            elements: [...state.elements, duplicatedElement],\n            selectedElementIds: [duplicatedElement.id],\n            history: {\n              past: [...state.history.past, state.history.present],\n              present: [...state.elements, duplicatedElement],\n              future: []\n            }\n          }\n        }),\n\n      duplicateElements: (ids) =>\n        set((state) => {\n          const elementsToDuplicate = state.elements.filter(e => ids.includes(e.id))\n          const duplicatedElements = elementsToDuplicate.map(element => ({\n            ...element,\n            id: generateId(),\n            position: {\n              x: element.position.x + 20,\n              y: element.position.y + 20\n            },\n            zIndex: Math.max(...state.elements.map(e => e.zIndex), 0) + 1\n          }))\n\n          return {\n            elements: [...state.elements, ...duplicatedElements],\n            selectedElementIds: duplicatedElements.map(e => e.id),\n            history: {\n              past: [...state.history.past, state.history.present],\n              present: [...state.elements, ...duplicatedElements],\n              future: []\n            }\n          }\n        }),\n\n      moveElement: (id, position) =>\n        get().updateElement(id, { position }),\n\n      resizeElement: (id, size) =>\n        get().updateElement(id, { size }),\n\n      rotateElement: (id, rotation) =>\n        get().updateElement(id, { rotation }),\n\n      bringToFront: (id) =>\n        set((state) => {\n          const maxZIndex = Math.max(...state.elements.map(e => e.zIndex))\n          return get().updateElement(id, { zIndex: maxZIndex + 1 })\n        }),\n\n      sendToBack: (id) =>\n        set((state) => {\n          const minZIndex = Math.min(...state.elements.map(e => e.zIndex))\n          return get().updateElement(id, { zIndex: minZIndex - 1 })\n        }),\n\n      copyElements: (ids) =>\n        set((state) => ({\n          clipboard: state.elements.filter(e => ids.includes(e.id))\n        })),\n\n      pasteElements: () =>\n        set((state) => {\n          const pastedElements = state.clipboard.map(element => ({\n            ...element,\n            id: generateId(),\n            position: {\n              x: element.position.x + 20,\n              y: element.position.y + 20\n            },\n            zIndex: Math.max(...state.elements.map(e => e.zIndex), 0) + 1\n          }))\n\n          return {\n            elements: [...state.elements, ...pastedElements],\n            selectedElementIds: pastedElements.map(e => e.id),\n            history: {\n              past: [...state.history.past, state.history.present],\n              present: [...state.elements, ...pastedElements],\n              future: []\n            }\n          }\n        }),\n\n      undo: () =>\n        set((state) => {\n          if (state.history.past.length === 0) return state\n          const previous = state.history.past[state.history.past.length - 1]\n          const newPast = state.history.past.slice(0, state.history.past.length - 1)\n          return {\n            elements: previous,\n            history: {\n              past: newPast,\n              present: previous,\n              future: [state.history.present, ...state.history.future]\n            }\n          }\n        }),\n\n      redo: () =>\n        set((state) => {\n          if (state.history.future.length === 0) return state\n          const next = state.history.future[0]\n          const newFuture = state.history.future.slice(1)\n          return {\n            elements: next,\n            history: {\n              past: [...state.history.past, state.history.present],\n              present: next,\n              future: newFuture\n            }\n          }\n        }),\n\n      setZoom: (zoom) => set({ zoom }),\n      setPan: (pan) => set({ pan }),\n      setTool: (tool) => set({ tool }),\n      setLoading: (isLoading) => set({ isLoading }),\n      setError: (error) => set({ error }),\n\n      reset: () => set(initialState),\n\n      loadProject: (project) =>\n        set({\n          canvas: project.canvas,\n          elements: project.elements,\n          selectedElementIds: [],\n          history: {\n            past: [],\n            present: project.elements,\n            future: []\n          }\n        })\n    }),\n    { name: 'editor-store' }\n  )\n)\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;;;;AAgCA,MAAM,eAA4B;IAChC,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,iBAAiB;IACnB;IACA,UAAU,EAAE;IACZ,oBAAoB,EAAE;IACtB,WAAW,EAAE;IACb,SAAS;QACP,MAAM,EAAE;QACR,SAAS,EAAE;QACX,QAAQ,EAAE;IACZ;IACA,MAAM;IACN,KAAK;QAAE,GAAG;QAAG,GAAG;IAAE;IAClB,MAAM;IACN,WAAW;IACX,OAAO;AACT;AAEO,MAAM,iBAAiB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IACjC,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EACL,CAAC,KAAK,MAAQ,CAAC;QACb,GAAG,YAAY;QAEf,WAAW,CAAC,SACV,IAAI,CAAC,QAAU,CAAC;oBACd,QAAQ;wBAAE,GAAG,MAAM,MAAM;wBAAE,GAAG,MAAM;oBAAC;gBACvC,CAAC;QAEH,YAAY,CAAC,UACX,IAAI,CAAC;gBACH,MAAM,aAA4B;oBAChC,GAAG,OAAO;oBACV,IAAI,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD;oBACb,QAAQ,KAAK,GAAG,IAAI,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM,GAAG,KAAK;gBAC9D;gBACA,OAAO;oBACL,UAAU;2BAAI,MAAM,QAAQ;wBAAE;qBAAW;oBACzC,SAAS;wBACP,MAAM;+BAAI,MAAM,OAAO,CAAC,IAAI;4BAAE,MAAM,OAAO,CAAC,OAAO;yBAAC;wBACpD,SAAS;+BAAI,MAAM,QAAQ;4BAAE;yBAAW;wBACxC,QAAQ,EAAE;oBACZ;gBACF;YACF;QAEF,eAAe,CAAC,IAAI,UAClB,IAAI,CAAC;gBACH,MAAM,kBAAkB,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAA,UACzC,QAAQ,EAAE,KAAK,KAAK;wBAAE,GAAG,OAAO;wBAAE,GAAG,OAAO;oBAAC,IAAI;gBAEnD,OAAO;oBACL,UAAU;oBACV,SAAS;wBACP,MAAM;+BAAI,MAAM,OAAO,CAAC,IAAI;4BAAE,MAAM,OAAO,CAAC,OAAO;yBAAC;wBACpD,SAAS;wBACT,QAAQ,EAAE;oBACZ;gBACF;YACF;QAEF,eAAe,CAAC,KACd,IAAI,CAAC;gBACH,MAAM,mBAAmB,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;gBACzE,OAAO;oBACL,UAAU;oBACV,oBAAoB,MAAM,kBAAkB,CAAC,MAAM,CAAC,CAAA,aAAc,eAAe;oBACjF,SAAS;wBACP,MAAM;+BAAI,MAAM,OAAO,CAAC,IAAI;4BAAE,MAAM,OAAO,CAAC,OAAO;yBAAC;wBACpD,SAAS;wBACT,QAAQ,EAAE;oBACZ;gBACF;YACF;QAEF,gBAAgB,CAAC,MACf,IAAI,CAAC;gBACH,MAAM,mBAAmB,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAA,UAAW,CAAC,IAAI,QAAQ,CAAC,QAAQ,EAAE;gBAClF,OAAO;oBACL,UAAU;oBACV,oBAAoB,MAAM,kBAAkB,CAAC,MAAM,CAAC,CAAA,aAAc,CAAC,IAAI,QAAQ,CAAC;oBAChF,SAAS;wBACP,MAAM;+BAAI,MAAM,OAAO,CAAC,IAAI;4BAAE,MAAM,OAAO,CAAC,OAAO;yBAAC;wBACpD,SAAS;wBACT,QAAQ,EAAE;oBACZ;gBACF;YACF;QAEF,eAAe,CAAC,KACd,IAAI;gBAAE,oBAAoB;oBAAC;iBAAG;YAAC;QAEjC,gBAAgB,CAAC,MACf,IAAI;gBAAE,oBAAoB;YAAI;QAEhC,aAAa,IACX,IAAI;gBAAE,oBAAoB,EAAE;YAAC;QAE/B,kBAAkB,CAAC,KACjB,IAAI,CAAC;gBACH,MAAM,UAAU,MAAM,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAClD,IAAI,CAAC,SAAS,OAAO;gBAErB,MAAM,oBAAmC;oBACvC,GAAG,OAAO;oBACV,IAAI,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD;oBACb,UAAU;wBACR,GAAG,QAAQ,QAAQ,CAAC,CAAC,GAAG;wBACxB,GAAG,QAAQ,QAAQ,CAAC,CAAC,GAAG;oBAC1B;oBACA,QAAQ,KAAK,GAAG,IAAI,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM,GAAG,KAAK;gBAC9D;gBAEA,OAAO;oBACL,UAAU;2BAAI,MAAM,QAAQ;wBAAE;qBAAkB;oBAChD,oBAAoB;wBAAC,kBAAkB,EAAE;qBAAC;oBAC1C,SAAS;wBACP,MAAM;+BAAI,MAAM,OAAO,CAAC,IAAI;4BAAE,MAAM,OAAO,CAAC,OAAO;yBAAC;wBACpD,SAAS;+BAAI,MAAM,QAAQ;4BAAE;yBAAkB;wBAC/C,QAAQ,EAAE;oBACZ;gBACF;YACF;QAEF,mBAAmB,CAAC,MAClB,IAAI,CAAC;gBACH,MAAM,sBAAsB,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,IAAI,QAAQ,CAAC,EAAE,EAAE;gBACxE,MAAM,qBAAqB,oBAAoB,GAAG,CAAC,CAAA,UAAW,CAAC;wBAC7D,GAAG,OAAO;wBACV,IAAI,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD;wBACb,UAAU;4BACR,GAAG,QAAQ,QAAQ,CAAC,CAAC,GAAG;4BACxB,GAAG,QAAQ,QAAQ,CAAC,CAAC,GAAG;wBAC1B;wBACA,QAAQ,KAAK,GAAG,IAAI,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM,GAAG,KAAK;oBAC9D,CAAC;gBAED,OAAO;oBACL,UAAU;2BAAI,MAAM,QAAQ;2BAAK;qBAAmB;oBACpD,oBAAoB,mBAAmB,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;oBACpD,SAAS;wBACP,MAAM;+BAAI,MAAM,OAAO,CAAC,IAAI;4BAAE,MAAM,OAAO,CAAC,OAAO;yBAAC;wBACpD,SAAS;+BAAI,MAAM,QAAQ;+BAAK;yBAAmB;wBACnD,QAAQ,EAAE;oBACZ;gBACF;YACF;QAEF,aAAa,CAAC,IAAI,WAChB,MAAM,aAAa,CAAC,IAAI;gBAAE;YAAS;QAErC,eAAe,CAAC,IAAI,OAClB,MAAM,aAAa,CAAC,IAAI;gBAAE;YAAK;QAEjC,eAAe,CAAC,IAAI,WAClB,MAAM,aAAa,CAAC,IAAI;gBAAE;YAAS;QAErC,cAAc,CAAC,KACb,IAAI,CAAC;gBACH,MAAM,YAAY,KAAK,GAAG,IAAI,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;gBAC9D,OAAO,MAAM,aAAa,CAAC,IAAI;oBAAE,QAAQ,YAAY;gBAAE;YACzD;QAEF,YAAY,CAAC,KACX,IAAI,CAAC;gBACH,MAAM,YAAY,KAAK,GAAG,IAAI,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;gBAC9D,OAAO,MAAM,aAAa,CAAC,IAAI;oBAAE,QAAQ,YAAY;gBAAE;YACzD;QAEF,cAAc,CAAC,MACb,IAAI,CAAC,QAAU,CAAC;oBACd,WAAW,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,IAAI,QAAQ,CAAC,EAAE,EAAE;gBACzD,CAAC;QAEH,eAAe,IACb,IAAI,CAAC;gBACH,MAAM,iBAAiB,MAAM,SAAS,CAAC,GAAG,CAAC,CAAA,UAAW,CAAC;wBACrD,GAAG,OAAO;wBACV,IAAI,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD;wBACb,UAAU;4BACR,GAAG,QAAQ,QAAQ,CAAC,CAAC,GAAG;4BACxB,GAAG,QAAQ,QAAQ,CAAC,CAAC,GAAG;wBAC1B;wBACA,QAAQ,KAAK,GAAG,IAAI,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM,GAAG,KAAK;oBAC9D,CAAC;gBAED,OAAO;oBACL,UAAU;2BAAI,MAAM,QAAQ;2BAAK;qBAAe;oBAChD,oBAAoB,eAAe,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;oBAChD,SAAS;wBACP,MAAM;+BAAI,MAAM,OAAO,CAAC,IAAI;4BAAE,MAAM,OAAO,CAAC,OAAO;yBAAC;wBACpD,SAAS;+BAAI,MAAM,QAAQ;+BAAK;yBAAe;wBAC/C,QAAQ,EAAE;oBACZ;gBACF;YACF;QAEF,MAAM,IACJ,IAAI,CAAC;gBACH,IAAI,MAAM,OAAO,CAAC,IAAI,CAAC,MAAM,KAAK,GAAG,OAAO;gBAC5C,MAAM,WAAW,MAAM,OAAO,CAAC,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE;gBAClE,MAAM,UAAU,MAAM,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG;gBACxE,OAAO;oBACL,UAAU;oBACV,SAAS;wBACP,MAAM;wBACN,SAAS;wBACT,QAAQ;4BAAC,MAAM,OAAO,CAAC,OAAO;+BAAK,MAAM,OAAO,CAAC,MAAM;yBAAC;oBAC1D;gBACF;YACF;QAEF,MAAM,IACJ,IAAI,CAAC;gBACH,IAAI,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM,KAAK,GAAG,OAAO;gBAC9C,MAAM,OAAO,MAAM,OAAO,CAAC,MAAM,CAAC,EAAE;gBACpC,MAAM,YAAY,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC7C,OAAO;oBACL,UAAU;oBACV,SAAS;wBACP,MAAM;+BAAI,MAAM,OAAO,CAAC,IAAI;4BAAE,MAAM,OAAO,CAAC,OAAO;yBAAC;wBACpD,SAAS;wBACT,QAAQ;oBACV;gBACF;YACF;QAEF,SAAS,CAAC,OAAS,IAAI;gBAAE;YAAK;QAC9B,QAAQ,CAAC,MAAQ,IAAI;gBAAE;YAAI;QAC3B,SAAS,CAAC,OAAS,IAAI;gBAAE;YAAK;QAC9B,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;QAC3C,UAAU,CAAC,QAAU,IAAI;gBAAE;YAAM;QAEjC,OAAO,IAAM,IAAI;QAEjB,aAAa,CAAC,UACZ,IAAI;gBACF,QAAQ,QAAQ,MAAM;gBACtB,UAAU,QAAQ,QAAQ;gBAC1B,oBAAoB,EAAE;gBACtB,SAAS;oBACP,MAAM,EAAE;oBACR,SAAS,QAAQ,QAAQ;oBACzB,QAAQ,EAAE;gBACZ;YACF;IACJ,CAAC,GACD;IAAE,MAAM;AAAe", "debugId": null}}, {"offset": {"line": 498, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/ubuntu/graphical_abstract/src/components/Header.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Button } from '@/components/ui/button'\nimport { \n  Download, \n  Save, \n  Undo, \n  Redo, \n  ZoomIn, \n  ZoomOut, \n  Share2,\n  <PERSON><PERSON><PERSON>,\n  User\n} from 'lucide-react'\nimport { useEditorStore } from '@/store/editorStore'\n\nexport function Header() {\n  const { \n    undo, \n    redo, \n    zoom, \n    setZoom, \n    history,\n    isLoading \n  } = useEditorStore()\n\n  const handleZoomIn = () => {\n    setZoom(Math.min(zoom * 1.2, 5))\n  }\n\n  const handleZoomOut = () => {\n    setZoom(Math.max(zoom / 1.2, 0.1))\n  }\n\n  const handleSave = () => {\n    // TODO: Implement save functionality\n    console.log('Save project')\n  }\n\n  const handleExport = () => {\n    // TODO: Implement export functionality\n    console.log('Export project')\n  }\n\n  const handleShare = () => {\n    // TODO: Implement share functionality\n    console.log('Share project')\n  }\n\n  return (\n    <header className=\"h-16 border-b bg-background flex items-center justify-between px-4\">\n      <div className=\"flex items-center space-x-4\">\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n            <span className=\"text-white font-bold text-sm\">GA</span>\n          </div>\n          <h1 className=\"text-xl font-bold\">Graphical Abstract Generator</h1>\n        </div>\n      </div>\n\n      <div className=\"flex items-center space-x-2\">\n        {/* Undo/Redo */}\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          onClick={undo}\n          disabled={history.past.length === 0}\n          title=\"Undo\"\n        >\n          <Undo className=\"h-4 w-4\" />\n        </Button>\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          onClick={redo}\n          disabled={history.future.length === 0}\n          title=\"Redo\"\n        >\n          <Redo className=\"h-4 w-4\" />\n        </Button>\n\n        <div className=\"w-px h-6 bg-border mx-2\" />\n\n        {/* Zoom Controls */}\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          onClick={handleZoomOut}\n          title=\"Zoom Out\"\n        >\n          <ZoomOut className=\"h-4 w-4\" />\n        </Button>\n        <span className=\"text-sm font-medium min-w-[60px] text-center\">\n          {Math.round(zoom * 100)}%\n        </span>\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          onClick={handleZoomIn}\n          title=\"Zoom In\"\n        >\n          <ZoomIn className=\"h-4 w-4\" />\n        </Button>\n\n        <div className=\"w-px h-6 bg-border mx-2\" />\n\n        {/* Action Buttons */}\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={handleSave}\n          disabled={isLoading}\n        >\n          <Save className=\"h-4 w-4 mr-2\" />\n          Save\n        </Button>\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={handleExport}\n          disabled={isLoading}\n        >\n          <Download className=\"h-4 w-4 mr-2\" />\n          Export\n        </Button>\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={handleShare}\n          disabled={isLoading}\n        >\n          <Share2 className=\"h-4 w-4 mr-2\" />\n          Share\n        </Button>\n\n        <div className=\"w-px h-6 bg-border mx-2\" />\n\n        {/* Settings and User */}\n        <Button variant=\"ghost\" size=\"icon\" title=\"Settings\">\n          <Settings className=\"h-4 w-4\" />\n        </Button>\n        <Button variant=\"ghost\" size=\"icon\" title=\"User Profile\">\n          <User className=\"h-4 w-4\" />\n        </Button>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;AAfA;;;;AAiBO,SAAS;;IACd,MAAM,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,OAAO,EACP,SAAS,EACV,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAEjB,MAAM,eAAe;QACnB,QAAQ,KAAK,GAAG,CAAC,OAAO,KAAK;IAC/B;IAEA,MAAM,gBAAgB;QACpB,QAAQ,KAAK,GAAG,CAAC,OAAO,KAAK;IAC/B;IAEA,MAAM,aAAa;QACjB,qCAAqC;QACrC,QAAQ,GAAG,CAAC;IACd;IAEA,MAAM,eAAe;QACnB,uCAAuC;QACvC,QAAQ,GAAG,CAAC;IACd;IAEA,MAAM,cAAc;QAClB,sCAAsC;QACtC,QAAQ,GAAG,CAAC;IACd;IAEA,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;0CAA+B;;;;;;;;;;;sCAEjD,6LAAC;4BAAG,WAAU;sCAAoB;;;;;;;;;;;;;;;;;0BAItC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU,QAAQ,IAAI,CAAC,MAAM,KAAK;wBAClC,OAAM;kCAEN,cAAA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU,QAAQ,MAAM,CAAC,MAAM,KAAK;wBACpC,OAAM;kCAEN,cAAA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAGlB,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,OAAM;kCAEN,cAAA,6LAAC,+MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;kCAErB,6LAAC;wBAAK,WAAU;;4BACb,KAAK,KAAK,CAAC,OAAO;4BAAK;;;;;;;kCAE1B,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,OAAM;kCAEN,cAAA,6LAAC,6MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAGpB,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAGnC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU;;0CAEV,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAGvC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU;;0CAEV,6LAAC,6MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAIrC,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAO,OAAM;kCACxC,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;kCAEtB,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAO,OAAM;kCACxC,cAAA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAK1B;GAnIgB;;QAQV,8HAAA,CAAA,iBAAc;;;KARJ", "debugId": null}}, {"offset": {"line": 809, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/ubuntu/graphical_abstract/src/components/Toolbar.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { \n  MousePointer, \n  Type, \n  Square, \n  Circle, \n  ArrowRight, \n  Minus, \n  Image, \n  Shapes,\n  Palette,\n  Upload\n} from 'lucide-react'\nimport { useEditorStore } from '@/store/editorStore'\nimport { CanvasElement } from '@/types'\n\nexport function Toolbar() {\n  const { tool, setTool, addElement, elements } = useEditorStore()\n\n  const tools = [\n    { id: 'select', icon: MousePointer, label: 'Select' },\n    { id: 'text', icon: Type, label: 'Text' },\n    { id: 'shape', icon: Square, label: 'Rectangle' },\n    { id: 'circle', icon: Circle, label: 'Circle' },\n    { id: 'arrow', icon: ArrowRight, label: 'Arrow' },\n    { id: 'line', icon: Minus, label: 'Line' },\n    { id: 'image', icon: Image, label: 'Image' }\n  ]\n\n  const handleToolSelect = (toolId: string) => {\n    setTool(toolId as any)\n  }\n\n  const handleAddShape = (shapeType: 'rectangle' | 'circle') => {\n    const element: Omit<CanvasElement, 'id'> = {\n      type: 'shape',\n      position: { x: 100, y: 100 },\n      size: { width: 100, height: 100 },\n      rotation: 0,\n      opacity: 1,\n      zIndex: elements.length,\n      properties: {\n        fill: '#3b82f6',\n        stroke: '',\n        strokeWidth: 0,\n        borderRadius: shapeType === 'circle' ? 50 : 0\n      }\n    }\n    addElement(element)\n  }\n\n  const handleAddText = () => {\n    const element: Omit<CanvasElement, 'id'> = {\n      type: 'text',\n      position: { x: 100, y: 100 },\n      size: { width: 200, height: 40 },\n      rotation: 0,\n      opacity: 1,\n      zIndex: elements.length,\n      properties: {\n        text: 'New Text',\n        fontSize: 16,\n        fontFamily: 'Arial',\n        color: '#000000',\n        textAlign: 'left'\n      }\n    }\n    addElement(element)\n  }\n\n  const handleAddArrow = () => {\n    const element: Omit<CanvasElement, 'id'> = {\n      type: 'arrow',\n      position: { x: 100, y: 100 },\n      size: { width: 150, height: 20 },\n      rotation: 0,\n      opacity: 1,\n      zIndex: elements.length,\n      properties: {\n        stroke: '#000000',\n        strokeWidth: 2,\n        arrowType: 'single'\n      }\n    }\n    addElement(element)\n  }\n\n  const handleAddLine = () => {\n    const element: Omit<CanvasElement, 'id'> = {\n      type: 'line',\n      position: { x: 100, y: 100 },\n      size: { width: 150, height: 0 },\n      rotation: 0,\n      opacity: 1,\n      zIndex: elements.length,\n      properties: {\n        stroke: '#000000',\n        strokeWidth: 2\n      }\n    }\n    addElement(element)\n  }\n\n  const handleImageUpload = () => {\n    const input = document.createElement('input')\n    input.type = 'file'\n    input.accept = 'image/*'\n    input.onchange = (e) => {\n      const file = (e.target as HTMLInputElement).files?.[0]\n      if (file) {\n        const reader = new FileReader()\n        reader.onload = (event) => {\n          const element: Omit<CanvasElement, 'id'> = {\n            type: 'image',\n            position: { x: 100, y: 100 },\n            size: { width: 200, height: 150 },\n            rotation: 0,\n            opacity: 1,\n            zIndex: elements.length,\n            properties: {\n              src: event.target?.result as string,\n              alt: file.name\n            }\n          }\n          addElement(element)\n        }\n        reader.readAsDataURL(file)\n      }\n    }\n    input.click()\n  }\n\n  return (\n    <div className=\"w-16 bg-gray-50 border-r flex flex-col items-center py-4 space-y-2\">\n      {tools.map((toolItem) => (\n        <Button\n          key={toolItem.id}\n          variant={tool === toolItem.id ? \"default\" : \"ghost\"}\n          size=\"icon\"\n          onClick={() => {\n            handleToolSelect(toolItem.id)\n            \n            // Auto-add elements for certain tools\n            switch (toolItem.id) {\n              case 'text':\n                handleAddText()\n                break\n              case 'shape':\n                handleAddShape('rectangle')\n                break\n              case 'circle':\n                handleAddShape('circle')\n                break\n              case 'arrow':\n                handleAddArrow()\n                break\n              case 'line':\n                handleAddLine()\n                break\n              case 'image':\n                handleImageUpload()\n                break\n            }\n          }}\n          title={toolItem.label}\n          className=\"w-12 h-12\"\n        >\n          <toolItem.icon className=\"h-5 w-5\" />\n        </Button>\n      ))}\n\n      <div className=\"w-8 h-px bg-gray-300 my-2\" />\n\n      {/* Quick Actions */}\n      <Button\n        variant=\"ghost\"\n        size=\"icon\"\n        onClick={() => handleAddShape('rectangle')}\n        title=\"Add Rectangle\"\n        className=\"w-12 h-12\"\n      >\n        <Square className=\"h-5 w-5\" />\n      </Button>\n\n      <Button\n        variant=\"ghost\"\n        size=\"icon\"\n        onClick={() => handleAddShape('circle')}\n        title=\"Add Circle\"\n        className=\"w-12 h-12\"\n      >\n        <Circle className=\"h-5 w-5\" />\n      </Button>\n\n      <Button\n        variant=\"ghost\"\n        size=\"icon\"\n        onClick={handleImageUpload}\n        title=\"Upload Image\"\n        className=\"w-12 h-12\"\n      >\n        <Upload className=\"h-5 w-5\" />\n      </Button>\n\n      <div className=\"w-8 h-px bg-gray-300 my-2\" />\n\n      {/* Color Palette */}\n      <div className=\"flex flex-col space-y-1\">\n        {['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#000000'].map((color) => (\n          <button\n            key={color}\n            className=\"w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform\"\n            style={{ backgroundColor: color }}\n            title={`Color: ${color}`}\n            onClick={() => {\n              // TODO: Apply color to selected elements\n              console.log('Apply color:', color)\n            }}\n          />\n        ))}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;;;AAhBA;;;;AAmBO,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAE7D,MAAM,QAAQ;QACZ;YAAE,IAAI;YAAU,MAAM,yNAAA,CAAA,eAAY;YAAE,OAAO;QAAS;QACpD;YAAE,IAAI;YAAQ,MAAM,qMAAA,CAAA,OAAI;YAAE,OAAO;QAAO;QACxC;YAAE,IAAI;YAAS,MAAM,yMAAA,CAAA,SAAM;YAAE,OAAO;QAAY;QAChD;YAAE,IAAI;YAAU,MAAM,yMAAA,CAAA,SAAM;YAAE,OAAO;QAAS;QAC9C;YAAE,IAAI;YAAS,MAAM,qNAAA,CAAA,aAAU;YAAE,OAAO;QAAQ;QAChD;YAAE,IAAI;YAAQ,MAAM,uMAAA,CAAA,QAAK;YAAE,OAAO;QAAO;QACzC;YAAE,IAAI;YAAS,MAAM,uMAAA,CAAA,QAAK;YAAE,OAAO;QAAQ;KAC5C;IAED,MAAM,mBAAmB,CAAC;QACxB,QAAQ;IACV;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,UAAqC;YACzC,MAAM;YACN,UAAU;gBAAE,GAAG;gBAAK,GAAG;YAAI;YAC3B,MAAM;gBAAE,OAAO;gBAAK,QAAQ;YAAI;YAChC,UAAU;YACV,SAAS;YACT,QAAQ,SAAS,MAAM;YACvB,YAAY;gBACV,MAAM;gBACN,QAAQ;gBACR,aAAa;gBACb,cAAc,cAAc,WAAW,KAAK;YAC9C;QACF;QACA,WAAW;IACb;IAEA,MAAM,gBAAgB;QACpB,MAAM,UAAqC;YACzC,MAAM;YACN,UAAU;gBAAE,GAAG;gBAAK,GAAG;YAAI;YAC3B,MAAM;gBAAE,OAAO;gBAAK,QAAQ;YAAG;YAC/B,UAAU;YACV,SAAS;YACT,QAAQ,SAAS,MAAM;YACvB,YAAY;gBACV,MAAM;gBACN,UAAU;gBACV,YAAY;gBACZ,OAAO;gBACP,WAAW;YACb;QACF;QACA,WAAW;IACb;IAEA,MAAM,iBAAiB;QACrB,MAAM,UAAqC;YACzC,MAAM;YACN,UAAU;gBAAE,GAAG;gBAAK,GAAG;YAAI;YAC3B,MAAM;gBAAE,OAAO;gBAAK,QAAQ;YAAG;YAC/B,UAAU;YACV,SAAS;YACT,QAAQ,SAAS,MAAM;YACvB,YAAY;gBACV,QAAQ;gBACR,aAAa;gBACb,WAAW;YACb;QACF;QACA,WAAW;IACb;IAEA,MAAM,gBAAgB;QACpB,MAAM,UAAqC;YACzC,MAAM;YACN,UAAU;gBAAE,GAAG;gBAAK,GAAG;YAAI;YAC3B,MAAM;gBAAE,OAAO;gBAAK,QAAQ;YAAE;YAC9B,UAAU;YACV,SAAS;YACT,QAAQ,SAAS,MAAM;YACvB,YAAY;gBACV,QAAQ;gBACR,aAAa;YACf;QACF;QACA,WAAW;IACb;IAEA,MAAM,oBAAoB;QACxB,MAAM,QAAQ,SAAS,aAAa,CAAC;QACrC,MAAM,IAAI,GAAG;QACb,MAAM,MAAM,GAAG;QACf,MAAM,QAAQ,GAAG,CAAC;YAChB,MAAM,OAAO,AAAC,EAAE,MAAM,CAAsB,KAAK,EAAE,CAAC,EAAE;YACtD,IAAI,MAAM;gBACR,MAAM,SAAS,IAAI;gBACnB,OAAO,MAAM,GAAG,CAAC;oBACf,MAAM,UAAqC;wBACzC,MAAM;wBACN,UAAU;4BAAE,GAAG;4BAAK,GAAG;wBAAI;wBAC3B,MAAM;4BAAE,OAAO;4BAAK,QAAQ;wBAAI;wBAChC,UAAU;wBACV,SAAS;wBACT,QAAQ,SAAS,MAAM;wBACvB,YAAY;4BACV,KAAK,MAAM,MAAM,EAAE;4BACnB,KAAK,KAAK,IAAI;wBAChB;oBACF;oBACA,WAAW;gBACb;gBACA,OAAO,aAAa,CAAC;YACvB;QACF;QACA,MAAM,KAAK;IACb;IAEA,qBACE,6LAAC;QAAI,WAAU;;YACZ,MAAM,GAAG,CAAC,CAAC,yBACV,6LAAC,qIAAA,CAAA,SAAM;oBAEL,SAAS,SAAS,SAAS,EAAE,GAAG,YAAY;oBAC5C,MAAK;oBACL,SAAS;wBACP,iBAAiB,SAAS,EAAE;wBAE5B,sCAAsC;wBACtC,OAAQ,SAAS,EAAE;4BACjB,KAAK;gCACH;gCACA;4BACF,KAAK;gCACH,eAAe;gCACf;4BACF,KAAK;gCACH,eAAe;gCACf;4BACF,KAAK;gCACH;gCACA;4BACF,KAAK;gCACH;gCACA;4BACF,KAAK;gCACH;gCACA;wBACJ;oBACF;oBACA,OAAO,SAAS,KAAK;oBACrB,WAAU;8BAEV,cAAA,6LAAC,SAAS,IAAI;wBAAC,WAAU;;;;;;mBA/BpB,SAAS,EAAE;;;;;0BAmCpB,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS,IAAM,eAAe;gBAC9B,OAAM;gBACN,WAAU;0BAEV,cAAA,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;;;;;;0BAGpB,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS,IAAM,eAAe;gBAC9B,OAAM;gBACN,WAAU;0BAEV,cAAA,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;;;;;;0BAGpB,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS;gBACT,OAAM;gBACN,WAAU;0BAEV,cAAA,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;;;;;;0BAGpB,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;0BACZ;oBAAC;oBAAW;oBAAW;oBAAW;oBAAW;oBAAW;iBAAU,CAAC,GAAG,CAAC,CAAC,sBACvE,6LAAC;wBAEC,WAAU;wBACV,OAAO;4BAAE,iBAAiB;wBAAM;wBAChC,OAAO,CAAC,OAAO,EAAE,OAAO;wBACxB,SAAS;4BACP,yCAAyC;4BACzC,QAAQ,GAAG,CAAC,gBAAgB;wBAC9B;uBAPK;;;;;;;;;;;;;;;;AAajB;GA/MgB;;QACkC,8HAAA,CAAA,iBAAc;;;KADhD", "debugId": null}}, {"offset": {"line": 1161, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/ubuntu/graphical_abstract/src/components/Canvas.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useEffect, useRef, useState } from 'react'\nimport { useEditorStore } from '@/store/editorStore'\nimport { CanvasElement } from '@/types'\n\nexport function Canvas() {\n  const canvasRef = useRef<HTMLCanvasElement>(null)\n  const [isInitialized, setIsInitialized] = useState(false)\n\n  const {\n    canvas: canvasConfig,\n    elements,\n    selectedElementIds,\n    zoom,\n    pan,\n    tool,\n    updateElement,\n    selectElement,\n    selectElements,\n    deselectAll,\n    moveElement,\n    resizeElement,\n    rotateElement\n  } = useEditorStore()\n\n  // For now, let's create a simple HTML5 canvas implementation\n  useEffect(() => {\n    if (!canvasRef.current || isInitialized) return\n\n    const canvas = canvasRef.current\n    const ctx = canvas.getContext('2d')\n\n    if (!ctx) return\n\n    canvas.width = canvasConfig.width\n    canvas.height = canvasConfig.height\n\n    setIsInitialized(true)\n\n    // Cleanup\n    return () => {\n      setIsInitialized(false)\n    }\n  }, [canvasRef.current])\n\n  // Update canvas configuration\n  useEffect(() => {\n    if (!canvasRef.current) return\n\n    const canvas = canvasRef.current\n    canvas.width = canvasConfig.width\n    canvas.height = canvasConfig.height\n\n    // Redraw canvas\n    drawCanvas()\n  }, [canvasConfig])\n\n  // Render elements on canvas\n  useEffect(() => {\n    drawCanvas()\n  }, [elements, canvasConfig.backgroundColor])\n\n  const drawCanvas = () => {\n    if (!canvasRef.current) return\n\n    const canvas = canvasRef.current\n    const ctx = canvas.getContext('2d')\n\n    if (!ctx) return\n\n    // Clear canvas\n    ctx.fillStyle = canvasConfig.backgroundColor\n    ctx.fillRect(0, 0, canvas.width, canvas.height)\n\n    // Draw elements\n    elements.forEach((element) => {\n      drawElement(ctx, element)\n    })\n  }\n\n  const drawElement = (ctx: CanvasRenderingContext2D, element: CanvasElement) => {\n    ctx.save()\n\n    // Apply transformations\n    ctx.globalAlpha = element.opacity\n    ctx.translate(element.position.x + element.size.width / 2, element.position.y + element.size.height / 2)\n    ctx.rotate((element.rotation * Math.PI) / 180)\n    ctx.translate(-element.size.width / 2, -element.size.height / 2)\n\n    switch (element.type) {\n      case 'text':\n        ctx.fillStyle = element.properties.color || '#000000'\n        ctx.font = `${element.properties.fontWeight || 'normal'} ${element.properties.fontSize || 16}px ${element.properties.fontFamily || 'Arial'}`\n        ctx.textAlign = (element.properties.textAlign as CanvasTextAlign) || 'left'\n        ctx.fillText(element.properties.text || '', 0, element.properties.fontSize || 16)\n        break\n\n      case 'shape':\n        ctx.fillStyle = element.properties.fill || '#3b82f6'\n        if (element.properties.borderRadius) {\n          // Draw rounded rectangle\n          const radius = element.properties.borderRadius\n          ctx.beginPath()\n          ctx.roundRect(0, 0, element.size.width, element.size.height, radius)\n          ctx.fill()\n        } else {\n          ctx.fillRect(0, 0, element.size.width, element.size.height)\n        }\n\n        if (element.properties.stroke) {\n          ctx.strokeStyle = element.properties.stroke\n          ctx.lineWidth = element.properties.strokeWidth || 1\n          ctx.strokeRect(0, 0, element.size.width, element.size.height)\n        }\n        break\n\n      case 'line':\n        ctx.strokeStyle = element.properties.stroke || '#000000'\n        ctx.lineWidth = element.properties.strokeWidth || 2\n        ctx.beginPath()\n        ctx.moveTo(0, element.size.height / 2)\n        ctx.lineTo(element.size.width, element.size.height / 2)\n        ctx.stroke()\n        break\n\n      case 'arrow':\n        ctx.strokeStyle = element.properties.stroke || '#000000'\n        ctx.fillStyle = element.properties.stroke || '#000000'\n        ctx.lineWidth = element.properties.strokeWidth || 2\n\n        // Draw line\n        ctx.beginPath()\n        ctx.moveTo(0, element.size.height / 2)\n        ctx.lineTo(element.size.width - 15, element.size.height / 2)\n        ctx.stroke()\n\n        // Draw arrowhead\n        ctx.beginPath()\n        ctx.moveTo(element.size.width, element.size.height / 2)\n        ctx.lineTo(element.size.width - 15, element.size.height / 2 - 5)\n        ctx.lineTo(element.size.width - 15, element.size.height / 2 + 5)\n        ctx.closePath()\n        ctx.fill()\n        break\n    }\n\n    ctx.restore()\n  }\n\n  const handleCanvasClick = (e: React.MouseEvent) => {\n    if (!canvasRef.current) return\n\n    const rect = canvasRef.current.getBoundingClientRect()\n    const x = e.clientX - rect.left\n    const y = e.clientY - rect.top\n\n    if (tool === 'text') {\n      // Add text element\n      const textElement: Omit<CanvasElement, 'id'> = {\n        type: 'text',\n        position: { x, y },\n        size: { width: 100, height: 30 },\n        rotation: 0,\n        opacity: 1,\n        zIndex: elements.length,\n        properties: {\n          text: 'New Text',\n          fontSize: 16,\n          fontFamily: 'Arial',\n          color: '#000000'\n        }\n      }\n\n      // This would be handled by the store\n      // addElement(textElement)\n    }\n  }\n\n  return (\n    <div className=\"flex-1 overflow-hidden bg-gray-100 relative\">\n      <div className=\"absolute inset-0 flex items-center justify-center\">\n        <div\n          className=\"bg-white shadow-lg\"\n          style={{\n            transform: `scale(${zoom})`,\n            transformOrigin: 'center center'\n          }}\n        >\n          <canvas\n            ref={canvasRef}\n            onClick={handleCanvasClick}\n            className=\"border border-gray-300 cursor-crosshair\"\n            style={{\n              width: canvasConfig.width,\n              height: canvasConfig.height\n            }}\n          />\n        </div>\n      </div>\n\n      {/* Selection indicators */}\n      {selectedElementIds.length > 0 && (\n        <div className=\"absolute top-4 right-4 bg-blue-500 text-white px-3 py-1 rounded text-sm\">\n          {selectedElementIds.length} element(s) selected\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAMO,SAAS;;IACd,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,EACJ,QAAQ,YAAY,EACpB,QAAQ,EACR,kBAAkB,EAClB,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,aAAa,EACb,aAAa,EACb,cAAc,EACd,WAAW,EACX,WAAW,EACX,aAAa,EACb,aAAa,EACd,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAEjB,6DAA6D;IAC7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,IAAI,CAAC,UAAU,OAAO,IAAI,eAAe;YAEzC,MAAM,SAAS,UAAU,OAAO;YAChC,MAAM,MAAM,OAAO,UAAU,CAAC;YAE9B,IAAI,CAAC,KAAK;YAEV,OAAO,KAAK,GAAG,aAAa,KAAK;YACjC,OAAO,MAAM,GAAG,aAAa,MAAM;YAEnC,iBAAiB;YAEjB,UAAU;YACV;oCAAO;oBACL,iBAAiB;gBACnB;;QACF;2BAAG;QAAC,UAAU,OAAO;KAAC;IAEtB,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,IAAI,CAAC,UAAU,OAAO,EAAE;YAExB,MAAM,SAAS,UAAU,OAAO;YAChC,OAAO,KAAK,GAAG,aAAa,KAAK;YACjC,OAAO,MAAM,GAAG,aAAa,MAAM;YAEnC,gBAAgB;YAChB;QACF;2BAAG;QAAC;KAAa;IAEjB,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR;QACF;2BAAG;QAAC;QAAU,aAAa,eAAe;KAAC;IAE3C,MAAM,aAAa;QACjB,IAAI,CAAC,UAAU,OAAO,EAAE;QAExB,MAAM,SAAS,UAAU,OAAO;QAChC,MAAM,MAAM,OAAO,UAAU,CAAC;QAE9B,IAAI,CAAC,KAAK;QAEV,eAAe;QACf,IAAI,SAAS,GAAG,aAAa,eAAe;QAC5C,IAAI,QAAQ,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;QAE9C,gBAAgB;QAChB,SAAS,OAAO,CAAC,CAAC;YAChB,YAAY,KAAK;QACnB;IACF;IAEA,MAAM,cAAc,CAAC,KAA+B;QAClD,IAAI,IAAI;QAER,wBAAwB;QACxB,IAAI,WAAW,GAAG,QAAQ,OAAO;QACjC,IAAI,SAAS,CAAC,QAAQ,QAAQ,CAAC,CAAC,GAAG,QAAQ,IAAI,CAAC,KAAK,GAAG,GAAG,QAAQ,QAAQ,CAAC,CAAC,GAAG,QAAQ,IAAI,CAAC,MAAM,GAAG;QACtG,IAAI,MAAM,CAAC,AAAC,QAAQ,QAAQ,GAAG,KAAK,EAAE,GAAI;QAC1C,IAAI,SAAS,CAAC,CAAC,QAAQ,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,QAAQ,IAAI,CAAC,MAAM,GAAG;QAE9D,OAAQ,QAAQ,IAAI;YAClB,KAAK;gBACH,IAAI,SAAS,GAAG,QAAQ,UAAU,CAAC,KAAK,IAAI;gBAC5C,IAAI,IAAI,GAAG,GAAG,QAAQ,UAAU,CAAC,UAAU,IAAI,SAAS,CAAC,EAAE,QAAQ,UAAU,CAAC,QAAQ,IAAI,GAAG,GAAG,EAAE,QAAQ,UAAU,CAAC,UAAU,IAAI,SAAS;gBAC5I,IAAI,SAAS,GAAG,AAAC,QAAQ,UAAU,CAAC,SAAS,IAAwB;gBACrE,IAAI,QAAQ,CAAC,QAAQ,UAAU,CAAC,IAAI,IAAI,IAAI,GAAG,QAAQ,UAAU,CAAC,QAAQ,IAAI;gBAC9E;YAEF,KAAK;gBACH,IAAI,SAAS,GAAG,QAAQ,UAAU,CAAC,IAAI,IAAI;gBAC3C,IAAI,QAAQ,UAAU,CAAC,YAAY,EAAE;oBACnC,yBAAyB;oBACzB,MAAM,SAAS,QAAQ,UAAU,CAAC,YAAY;oBAC9C,IAAI,SAAS;oBACb,IAAI,SAAS,CAAC,GAAG,GAAG,QAAQ,IAAI,CAAC,KAAK,EAAE,QAAQ,IAAI,CAAC,MAAM,EAAE;oBAC7D,IAAI,IAAI;gBACV,OAAO;oBACL,IAAI,QAAQ,CAAC,GAAG,GAAG,QAAQ,IAAI,CAAC,KAAK,EAAE,QAAQ,IAAI,CAAC,MAAM;gBAC5D;gBAEA,IAAI,QAAQ,UAAU,CAAC,MAAM,EAAE;oBAC7B,IAAI,WAAW,GAAG,QAAQ,UAAU,CAAC,MAAM;oBAC3C,IAAI,SAAS,GAAG,QAAQ,UAAU,CAAC,WAAW,IAAI;oBAClD,IAAI,UAAU,CAAC,GAAG,GAAG,QAAQ,IAAI,CAAC,KAAK,EAAE,QAAQ,IAAI,CAAC,MAAM;gBAC9D;gBACA;YAEF,KAAK;gBACH,IAAI,WAAW,GAAG,QAAQ,UAAU,CAAC,MAAM,IAAI;gBAC/C,IAAI,SAAS,GAAG,QAAQ,UAAU,CAAC,WAAW,IAAI;gBAClD,IAAI,SAAS;gBACb,IAAI,MAAM,CAAC,GAAG,QAAQ,IAAI,CAAC,MAAM,GAAG;gBACpC,IAAI,MAAM,CAAC,QAAQ,IAAI,CAAC,KAAK,EAAE,QAAQ,IAAI,CAAC,MAAM,GAAG;gBACrD,IAAI,MAAM;gBACV;YAEF,KAAK;gBACH,IAAI,WAAW,GAAG,QAAQ,UAAU,CAAC,MAAM,IAAI;gBAC/C,IAAI,SAAS,GAAG,QAAQ,UAAU,CAAC,MAAM,IAAI;gBAC7C,IAAI,SAAS,GAAG,QAAQ,UAAU,CAAC,WAAW,IAAI;gBAElD,YAAY;gBACZ,IAAI,SAAS;gBACb,IAAI,MAAM,CAAC,GAAG,QAAQ,IAAI,CAAC,MAAM,GAAG;gBACpC,IAAI,MAAM,CAAC,QAAQ,IAAI,CAAC,KAAK,GAAG,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG;gBAC1D,IAAI,MAAM;gBAEV,iBAAiB;gBACjB,IAAI,SAAS;gBACb,IAAI,MAAM,CAAC,QAAQ,IAAI,CAAC,KAAK,EAAE,QAAQ,IAAI,CAAC,MAAM,GAAG;gBACrD,IAAI,MAAM,CAAC,QAAQ,IAAI,CAAC,KAAK,GAAG,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG,IAAI;gBAC9D,IAAI,MAAM,CAAC,QAAQ,IAAI,CAAC,KAAK,GAAG,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG,IAAI;gBAC9D,IAAI,SAAS;gBACb,IAAI,IAAI;gBACR;QACJ;QAEA,IAAI,OAAO;IACb;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,CAAC,UAAU,OAAO,EAAE;QAExB,MAAM,OAAO,UAAU,OAAO,CAAC,qBAAqB;QACpD,MAAM,IAAI,EAAE,OAAO,GAAG,KAAK,IAAI;QAC/B,MAAM,IAAI,EAAE,OAAO,GAAG,KAAK,GAAG;QAE9B,IAAI,SAAS,QAAQ;YACnB,mBAAmB;YACnB,MAAM,cAAyC;gBAC7C,MAAM;gBACN,UAAU;oBAAE;oBAAG;gBAAE;gBACjB,MAAM;oBAAE,OAAO;oBAAK,QAAQ;gBAAG;gBAC/B,UAAU;gBACV,SAAS;gBACT,QAAQ,SAAS,MAAM;gBACvB,YAAY;oBACV,MAAM;oBACN,UAAU;oBACV,YAAY;oBACZ,OAAO;gBACT;YACF;QAEA,qCAAqC;QACrC,0BAA0B;QAC5B;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBACL,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;wBAC3B,iBAAiB;oBACnB;8BAEA,cAAA,6LAAC;wBACC,KAAK;wBACL,SAAS;wBACT,WAAU;wBACV,OAAO;4BACL,OAAO,aAAa,KAAK;4BACzB,QAAQ,aAAa,MAAM;wBAC7B;;;;;;;;;;;;;;;;YAML,mBAAmB,MAAM,GAAG,mBAC3B,6LAAC;gBAAI,WAAU;;oBACZ,mBAAmB,MAAM;oBAAC;;;;;;;;;;;;;AAKrC;GA3MgB;;QAkBV,8HAAA,CAAA,iBAAc;;;KAlBJ", "debugId": null}}, {"offset": {"line": 1391, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/ubuntu/graphical_abstract/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1427, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/ubuntu/graphical_abstract/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1530, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/ubuntu/graphical_abstract/src/components/ui/slider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Slider = React.forwardRef<\n  React.ElementRef<typeof SliderPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <SliderPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex w-full touch-none select-none items-center\",\n      className\n    )}\n    {...props}\n  >\n    <SliderPrimitive.Track className=\"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary\">\n      <SliderPrimitive.Range className=\"absolute h-full bg-primary\" />\n    </SliderPrimitive.Track>\n    <SliderPrimitive.Thumb className=\"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\" />\n  </SliderPrimitive.Root>\n))\nSlider.displayName = SliderPrimitive.Root.displayName\n\nexport { Slider }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;0BAET,6LAAC,qKAAA,CAAA,QAAqB;gBAAC,WAAU;0BAC/B,cAAA,6LAAC,qKAAA,CAAA,QAAqB;oBAAC,WAAU;;;;;;;;;;;0BAEnC,6LAAC,qKAAA,CAAA,QAAqB;gBAAC,WAAU;;;;;;;;;;;;;AAGrC,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1589, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/ubuntu/graphical_abstract/src/components/PropertiesPanel.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { <PERSON>lider } from '@/components/ui/slider'\nimport { \n  Trash2, \n  <PERSON><PERSON>, \n  <PERSON>ota<PERSON><PERSON>w, \n  Move3<PERSON>, \n  <PERSON><PERSON>,\n  Type,\n  Square\n} from 'lucide-react'\nimport { useEditorStore } from '@/store/editorStore'\nimport { HexColorPicker } from 'react-colorful'\n\nexport function PropertiesPanel() {\n  const {\n    elements,\n    selectedElementIds,\n    updateElement,\n    deleteElements,\n    duplicateElements,\n    bringToFront,\n    sendToBack\n  } = useEditorStore()\n\n  const selectedElements = elements.filter(el => selectedElementIds.includes(el.id))\n  const selectedElement = selectedElements.length === 1 ? selectedElements[0] : null\n\n  if (selectedElementIds.length === 0) {\n    return (\n      <div className=\"w-80 bg-gray-50 border-l p-4\">\n        <div className=\"text-center text-gray-500 mt-8\">\n          <Square className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\n          <p>Select an element to edit its properties</p>\n        </div>\n      </div>\n    )\n  }\n\n  const handlePropertyChange = (property: string, value: any) => {\n    selectedElementIds.forEach(id => {\n      updateElement(id, {\n        properties: {\n          ...elements.find(el => el.id === id)?.properties,\n          [property]: value\n        }\n      })\n    })\n  }\n\n  const handlePositionChange = (axis: 'x' | 'y', value: number) => {\n    selectedElementIds.forEach(id => {\n      const element = elements.find(el => el.id === id)\n      if (element) {\n        updateElement(id, {\n          position: {\n            ...element.position,\n            [axis]: value\n          }\n        })\n      }\n    })\n  }\n\n  const handleSizeChange = (dimension: 'width' | 'height', value: number) => {\n    selectedElementIds.forEach(id => {\n      const element = elements.find(el => el.id === id)\n      if (element) {\n        updateElement(id, {\n          size: {\n            ...element.size,\n            [dimension]: value\n          }\n        })\n      }\n    })\n  }\n\n  const handleOpacityChange = (value: number[]) => {\n    selectedElementIds.forEach(id => {\n      updateElement(id, { opacity: value[0] / 100 })\n    })\n  }\n\n  const handleRotationChange = (value: number[]) => {\n    selectedElementIds.forEach(id => {\n      updateElement(id, { rotation: value[0] })\n    })\n  }\n\n  return (\n    <div className=\"w-80 bg-gray-50 border-l overflow-y-auto\">\n      <div className=\"p-4 space-y-4\">\n        {/* Element Info */}\n        <Card>\n          <CardHeader className=\"pb-3\">\n            <CardTitle className=\"text-sm flex items-center gap-2\">\n              <Move3D className=\"h-4 w-4\" />\n              {selectedElementIds.length === 1 \n                ? `${selectedElement?.type} Element` \n                : `${selectedElementIds.length} Elements Selected`\n              }\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-3\">\n            {/* Actions */}\n            <div className=\"flex gap-2\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => duplicateElements(selectedElementIds)}\n                className=\"flex-1\"\n              >\n                <Copy className=\"h-3 w-3 mr-1\" />\n                Copy\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => deleteElements(selectedElementIds)}\n                className=\"flex-1\"\n              >\n                <Trash2 className=\"h-3 w-3 mr-1\" />\n                Delete\n              </Button>\n            </div>\n\n            {/* Layer Controls */}\n            <div className=\"flex gap-2\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => selectedElementIds.forEach(bringToFront)}\n                className=\"flex-1\"\n              >\n                To Front\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => selectedElementIds.forEach(sendToBack)}\n                className=\"flex-1\"\n              >\n                To Back\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Position & Size */}\n        {selectedElement && (\n          <Card>\n            <CardHeader className=\"pb-3\">\n              <CardTitle className=\"text-sm\">Position & Size</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              <div className=\"grid grid-cols-2 gap-2\">\n                <div>\n                  <label className=\"text-xs text-gray-500\">X</label>\n                  <Input\n                    type=\"number\"\n                    value={Math.round(selectedElement.position.x)}\n                    onChange={(e) => handlePositionChange('x', Number(e.target.value))}\n                    className=\"h-8\"\n                  />\n                </div>\n                <div>\n                  <label className=\"text-xs text-gray-500\">Y</label>\n                  <Input\n                    type=\"number\"\n                    value={Math.round(selectedElement.position.y)}\n                    onChange={(e) => handlePositionChange('y', Number(e.target.value))}\n                    className=\"h-8\"\n                  />\n                </div>\n              </div>\n              <div className=\"grid grid-cols-2 gap-2\">\n                <div>\n                  <label className=\"text-xs text-gray-500\">Width</label>\n                  <Input\n                    type=\"number\"\n                    value={Math.round(selectedElement.size.width)}\n                    onChange={(e) => handleSizeChange('width', Number(e.target.value))}\n                    className=\"h-8\"\n                  />\n                </div>\n                <div>\n                  <label className=\"text-xs text-gray-500\">Height</label>\n                  <Input\n                    type=\"number\"\n                    value={Math.round(selectedElement.size.height)}\n                    onChange={(e) => handleSizeChange('height', Number(e.target.value))}\n                    className=\"h-8\"\n                  />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        )}\n\n        {/* Transform */}\n        {selectedElement && (\n          <Card>\n            <CardHeader className=\"pb-3\">\n              <CardTitle className=\"text-sm flex items-center gap-2\">\n                <RotateCw className=\"h-4 w-4\" />\n                Transform\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div>\n                <label className=\"text-xs text-gray-500 mb-2 block\">\n                  Opacity: {Math.round(selectedElement.opacity * 100)}%\n                </label>\n                <Slider\n                  value={[selectedElement.opacity * 100]}\n                  onValueChange={handleOpacityChange}\n                  max={100}\n                  step={1}\n                  className=\"w-full\"\n                />\n              </div>\n              <div>\n                <label className=\"text-xs text-gray-500 mb-2 block\">\n                  Rotation: {Math.round(selectedElement.rotation)}°\n                </label>\n                <Slider\n                  value={[selectedElement.rotation]}\n                  onValueChange={handleRotationChange}\n                  min={-180}\n                  max={180}\n                  step={1}\n                  className=\"w-full\"\n                />\n              </div>\n            </CardContent>\n          </Card>\n        )}\n\n        {/* Text Properties */}\n        {selectedElement?.type === 'text' && (\n          <Card>\n            <CardHeader className=\"pb-3\">\n              <CardTitle className=\"text-sm flex items-center gap-2\">\n                <Type className=\"h-4 w-4\" />\n                Text Properties\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              <div>\n                <label className=\"text-xs text-gray-500\">Text</label>\n                <Input\n                  value={selectedElement.properties.text || ''}\n                  onChange={(e) => handlePropertyChange('text', e.target.value)}\n                  className=\"h-8\"\n                />\n              </div>\n              <div className=\"grid grid-cols-2 gap-2\">\n                <div>\n                  <label className=\"text-xs text-gray-500\">Font Size</label>\n                  <Input\n                    type=\"number\"\n                    value={selectedElement.properties.fontSize || 16}\n                    onChange={(e) => handlePropertyChange('fontSize', Number(e.target.value))}\n                    className=\"h-8\"\n                  />\n                </div>\n                <div>\n                  <label className=\"text-xs text-gray-500\">Font Family</label>\n                  <select\n                    value={selectedElement.properties.fontFamily || 'Arial'}\n                    onChange={(e) => handlePropertyChange('fontFamily', e.target.value)}\n                    className=\"h-8 w-full rounded border border-input bg-background px-2 text-sm\"\n                  >\n                    <option value=\"Arial\">Arial</option>\n                    <option value=\"Helvetica\">Helvetica</option>\n                    <option value=\"Times New Roman\">Times New Roman</option>\n                    <option value=\"Georgia\">Georgia</option>\n                    <option value=\"Verdana\">Verdana</option>\n                  </select>\n                </div>\n              </div>\n              <div>\n                <label className=\"text-xs text-gray-500 mb-2 block\">Text Color</label>\n                <HexColorPicker\n                  color={selectedElement.properties.color || '#000000'}\n                  onChange={(color) => handlePropertyChange('color', color)}\n                  style={{ width: '100%', height: '120px' }}\n                />\n              </div>\n            </CardContent>\n          </Card>\n        )}\n\n        {/* Shape Properties */}\n        {selectedElement?.type === 'shape' && (\n          <Card>\n            <CardHeader className=\"pb-3\">\n              <CardTitle className=\"text-sm flex items-center gap-2\">\n                <Palette className=\"h-4 w-4\" />\n                Shape Properties\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              <div>\n                <label className=\"text-xs text-gray-500 mb-2 block\">Fill Color</label>\n                <HexColorPicker\n                  color={selectedElement.properties.fill || '#3b82f6'}\n                  onChange={(color) => handlePropertyChange('fill', color)}\n                  style={{ width: '100%', height: '120px' }}\n                />\n              </div>\n              <div>\n                <label className=\"text-xs text-gray-500\">Border Radius</label>\n                <Input\n                  type=\"number\"\n                  value={selectedElement.properties.borderRadius || 0}\n                  onChange={(e) => handlePropertyChange('borderRadius', Number(e.target.value))}\n                  className=\"h-8\"\n                />\n              </div>\n            </CardContent>\n          </Card>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;;;AAjBA;;;;;;;;AAmBO,SAAS;;IACd,MAAM,EACJ,QAAQ,EACR,kBAAkB,EAClB,aAAa,EACb,cAAc,EACd,iBAAiB,EACjB,YAAY,EACZ,UAAU,EACX,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAEjB,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,KAAM,mBAAmB,QAAQ,CAAC,GAAG,EAAE;IAChF,MAAM,kBAAkB,iBAAiB,MAAM,KAAK,IAAI,gBAAgB,CAAC,EAAE,GAAG;IAE9E,IAAI,mBAAmB,MAAM,KAAK,GAAG;QACnC,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,6LAAC;kCAAE;;;;;;;;;;;;;;;;;IAIX;IAEA,MAAM,uBAAuB,CAAC,UAAkB;QAC9C,mBAAmB,OAAO,CAAC,CAAA;YACzB,cAAc,IAAI;gBAChB,YAAY;oBACV,GAAG,SAAS,IAAI,CAAC,CAAA,KAAM,GAAG,EAAE,KAAK,KAAK,UAAU;oBAChD,CAAC,SAAS,EAAE;gBACd;YACF;QACF;IACF;IAEA,MAAM,uBAAuB,CAAC,MAAiB;QAC7C,mBAAmB,OAAO,CAAC,CAAA;YACzB,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,KAAM,GAAG,EAAE,KAAK;YAC9C,IAAI,SAAS;gBACX,cAAc,IAAI;oBAChB,UAAU;wBACR,GAAG,QAAQ,QAAQ;wBACnB,CAAC,KAAK,EAAE;oBACV;gBACF;YACF;QACF;IACF;IAEA,MAAM,mBAAmB,CAAC,WAA+B;QACvD,mBAAmB,OAAO,CAAC,CAAA;YACzB,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,KAAM,GAAG,EAAE,KAAK;YAC9C,IAAI,SAAS;gBACX,cAAc,IAAI;oBAChB,MAAM;wBACJ,GAAG,QAAQ,IAAI;wBACf,CAAC,UAAU,EAAE;oBACf;gBACF;YACF;QACF;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,mBAAmB,OAAO,CAAC,CAAA;YACzB,cAAc,IAAI;gBAAE,SAAS,KAAK,CAAC,EAAE,GAAG;YAAI;QAC9C;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,mBAAmB,OAAO,CAAC,CAAA;YACzB,cAAc,IAAI;gBAAE,UAAU,KAAK,CAAC,EAAE;YAAC;QACzC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;4BAAC,WAAU;sCACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCACjB,mBAAmB,MAAM,KAAK,IAC3B,GAAG,iBAAiB,KAAK,QAAQ,CAAC,GAClC,GAAG,mBAAmB,MAAM,CAAC,kBAAkB,CAAC;;;;;;;;;;;;sCAIxD,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CAErB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,kBAAkB;4CACjC,WAAU;;8DAEV,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,eAAe;4CAC9B,WAAU;;8DAEV,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;8CAMvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,mBAAmB,OAAO,CAAC;4CAC1C,WAAU;sDACX;;;;;;sDAGD,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,mBAAmB,OAAO,CAAC;4CAC1C,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;gBAQN,iCACC,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;4BAAC,WAAU;sCACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAU;;;;;;;;;;;sCAEjC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAwB;;;;;;8DACzC,6LAAC,oIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,OAAO,KAAK,KAAK,CAAC,gBAAgB,QAAQ,CAAC,CAAC;oDAC5C,UAAU,CAAC,IAAM,qBAAqB,KAAK,OAAO,EAAE,MAAM,CAAC,KAAK;oDAChE,WAAU;;;;;;;;;;;;sDAGd,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAwB;;;;;;8DACzC,6LAAC,oIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,OAAO,KAAK,KAAK,CAAC,gBAAgB,QAAQ,CAAC,CAAC;oDAC5C,UAAU,CAAC,IAAM,qBAAqB,KAAK,OAAO,EAAE,MAAM,CAAC,KAAK;oDAChE,WAAU;;;;;;;;;;;;;;;;;;8CAIhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAwB;;;;;;8DACzC,6LAAC,oIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,OAAO,KAAK,KAAK,CAAC,gBAAgB,IAAI,CAAC,KAAK;oDAC5C,UAAU,CAAC,IAAM,iBAAiB,SAAS,OAAO,EAAE,MAAM,CAAC,KAAK;oDAChE,WAAU;;;;;;;;;;;;sDAGd,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAwB;;;;;;8DACzC,6LAAC,oIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,OAAO,KAAK,KAAK,CAAC,gBAAgB,IAAI,CAAC,MAAM;oDAC7C,UAAU,CAAC,IAAM,iBAAiB,UAAU,OAAO,EAAE,MAAM,CAAC,KAAK;oDACjE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBASrB,iCACC,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;4BAAC,WAAU;sCACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;sCAIpC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;;gDAAmC;gDACxC,KAAK,KAAK,CAAC,gBAAgB,OAAO,GAAG;gDAAK;;;;;;;sDAEtD,6LAAC,qIAAA,CAAA,SAAM;4CACL,OAAO;gDAAC,gBAAgB,OAAO,GAAG;6CAAI;4CACtC,eAAe;4CACf,KAAK;4CACL,MAAM;4CACN,WAAU;;;;;;;;;;;;8CAGd,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;;gDAAmC;gDACvC,KAAK,KAAK,CAAC,gBAAgB,QAAQ;gDAAE;;;;;;;sDAElD,6LAAC,qIAAA,CAAA,SAAM;4CACL,OAAO;gDAAC,gBAAgB,QAAQ;6CAAC;4CACjC,eAAe;4CACf,KAAK,CAAC;4CACN,KAAK;4CACL,MAAM;4CACN,WAAU;;;;;;;;;;;;;;;;;;;;;;;;gBAQnB,iBAAiB,SAAS,wBACzB,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;4BAAC,WAAU;sCACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;sCAIhC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAwB;;;;;;sDACzC,6LAAC,oIAAA,CAAA,QAAK;4CACJ,OAAO,gBAAgB,UAAU,CAAC,IAAI,IAAI;4CAC1C,UAAU,CAAC,IAAM,qBAAqB,QAAQ,EAAE,MAAM,CAAC,KAAK;4CAC5D,WAAU;;;;;;;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAwB;;;;;;8DACzC,6LAAC,oIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,OAAO,gBAAgB,UAAU,CAAC,QAAQ,IAAI;oDAC9C,UAAU,CAAC,IAAM,qBAAqB,YAAY,OAAO,EAAE,MAAM,CAAC,KAAK;oDACvE,WAAU;;;;;;;;;;;;sDAGd,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAwB;;;;;;8DACzC,6LAAC;oDACC,OAAO,gBAAgB,UAAU,CAAC,UAAU,IAAI;oDAChD,UAAU,CAAC,IAAM,qBAAqB,cAAc,EAAE,MAAM,CAAC,KAAK;oDAClE,WAAU;;sEAEV,6LAAC;4DAAO,OAAM;sEAAQ;;;;;;sEACtB,6LAAC;4DAAO,OAAM;sEAAY;;;;;;sEAC1B,6LAAC;4DAAO,OAAM;sEAAkB;;;;;;sEAChC,6LAAC;4DAAO,OAAM;sEAAU;;;;;;sEACxB,6LAAC;4DAAO,OAAM;sEAAU;;;;;;;;;;;;;;;;;;;;;;;;8CAI9B,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAmC;;;;;;sDACpD,6LAAC,sJAAA,CAAA,iBAAc;4CACb,OAAO,gBAAgB,UAAU,CAAC,KAAK,IAAI;4CAC3C,UAAU,CAAC,QAAU,qBAAqB,SAAS;4CACnD,OAAO;gDAAE,OAAO;gDAAQ,QAAQ;4CAAQ;;;;;;;;;;;;;;;;;;;;;;;;gBAQjD,iBAAiB,SAAS,yBACzB,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;4BAAC,WAAU;sCACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;sCAInC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAmC;;;;;;sDACpD,6LAAC,sJAAA,CAAA,iBAAc;4CACb,OAAO,gBAAgB,UAAU,CAAC,IAAI,IAAI;4CAC1C,UAAU,CAAC,QAAU,qBAAqB,QAAQ;4CAClD,OAAO;gDAAE,OAAO;gDAAQ,QAAQ;4CAAQ;;;;;;;;;;;;8CAG5C,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAwB;;;;;;sDACzC,6LAAC,oIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,OAAO,gBAAgB,UAAU,CAAC,YAAY,IAAI;4CAClD,UAAU,CAAC,IAAM,qBAAqB,gBAAgB,OAAO,EAAE,MAAM,CAAC,KAAK;4CAC3E,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5B;GAzTgB;;QASV,8HAAA,CAAA,iBAAc;;;KATJ", "debugId": null}}, {"offset": {"line": 2411, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/ubuntu/graphical_abstract/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2446, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/ubuntu/graphical_abstract/src/components/AIGenerationForm.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { <PERSON><PERSON><PERSON>, Loader2 } from 'lucide-react'\nimport { useEditorStore } from '@/store/editorStore'\nimport { AIGenerationRequest } from '@/types'\n\ninterface AIGenerationFormProps {\n  onGenerate: (request: AIGenerationRequest) => Promise<void>\n}\n\nexport function AIGenerationForm({ onGenerate }: AIGenerationFormProps) {\n  const [title, setTitle] = useState('')\n  const [abstract, setAbstract] = useState('')\n  const [field, setField] = useState('')\n  const [style, setStyle] = useState<'modern' | 'academic' | 'minimal' | 'colorful'>('modern')\n  const [elements, setElements] = useState<string[]>([])\n  const [isGenerating, setIsGenerating] = useState(false)\n\n  const { isLoading } = useEditorStore()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!title.trim() || !abstract.trim()) {\n      return\n    }\n\n    setIsGenerating(true)\n    \n    try {\n      await onGenerate({\n        title: title.trim(),\n        abstract: abstract.trim(),\n        field: field.trim() || 'General Science',\n        style,\n        elements\n      })\n    } catch (error) {\n      console.error('Generation failed:', error)\n    } finally {\n      setIsGenerating(false)\n    }\n  }\n\n  const handleElementToggle = (element: string) => {\n    setElements(prev => \n      prev.includes(element) \n        ? prev.filter(e => e !== element)\n        : [...prev, element]\n    )\n  }\n\n  const suggestedElements = [\n    'Molecules', 'Cells', 'DNA', 'Proteins', 'Graphs', 'Charts',\n    'Arrows', 'Process Flow', 'Timeline', 'Comparison', 'Results',\n    'Methods', 'Hypothesis', 'Conclusion', 'Data Visualization'\n  ]\n\n  const styles = [\n    { value: 'modern', label: 'Modern', description: 'Clean, contemporary design' },\n    { value: 'academic', label: 'Academic', description: 'Traditional scientific style' },\n    { value: 'minimal', label: 'Minimal', description: 'Simple, focused layout' },\n    { value: 'colorful', label: 'Colorful', description: 'Vibrant, engaging visuals' }\n  ]\n\n  return (\n    <Card className=\"w-full max-w-2xl\">\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <Sparkles className=\"h-5 w-5 text-blue-500\" />\n          AI-Powered Generation\n        </CardTitle>\n        <CardDescription>\n          Enter your manuscript details to generate a professional graphical abstract\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          <div className=\"space-y-2\">\n            <label htmlFor=\"title\" className=\"text-sm font-medium\">\n              Manuscript Title *\n            </label>\n            <Input\n              id=\"title\"\n              value={title}\n              onChange={(e) => setTitle(e.target.value)}\n              placeholder=\"Enter your manuscript title...\"\n              required\n            />\n          </div>\n\n          <div className=\"space-y-2\">\n            <label htmlFor=\"abstract\" className=\"text-sm font-medium\">\n              Abstract *\n            </label>\n            <Textarea\n              id=\"abstract\"\n              value={abstract}\n              onChange={(e) => setAbstract(e.target.value)}\n              placeholder=\"Paste your abstract here...\"\n              className=\"min-h-[120px]\"\n              required\n            />\n          </div>\n\n          <div className=\"space-y-2\">\n            <label htmlFor=\"field\" className=\"text-sm font-medium\">\n              Research Field\n            </label>\n            <Input\n              id=\"field\"\n              value={field}\n              onChange={(e) => setField(e.target.value)}\n              placeholder=\"e.g., Biology, Chemistry, Physics...\"\n            />\n          </div>\n\n          <div className=\"space-y-3\">\n            <label className=\"text-sm font-medium\">Style Preference</label>\n            <div className=\"grid grid-cols-2 gap-3\">\n              {styles.map((styleOption) => (\n                <button\n                  key={styleOption.value}\n                  type=\"button\"\n                  onClick={() => setStyle(styleOption.value as any)}\n                  className={`p-3 rounded-lg border text-left transition-colors ${\n                    style === styleOption.value\n                      ? 'border-blue-500 bg-blue-50 text-blue-900'\n                      : 'border-gray-200 hover:border-gray-300'\n                  }`}\n                >\n                  <div className=\"font-medium\">{styleOption.label}</div>\n                  <div className=\"text-xs text-gray-500\">{styleOption.description}</div>\n                </button>\n              ))}\n            </div>\n          </div>\n\n          <div className=\"space-y-3\">\n            <label className=\"text-sm font-medium\">\n              Suggested Elements (Optional)\n            </label>\n            <div className=\"flex flex-wrap gap-2\">\n              {suggestedElements.map((element) => (\n                <button\n                  key={element}\n                  type=\"button\"\n                  onClick={() => handleElementToggle(element)}\n                  className={`px-3 py-1 rounded-full text-sm transition-colors ${\n                    elements.includes(element)\n                      ? 'bg-blue-500 text-white'\n                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                  }`}\n                >\n                  {element}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          <Button\n            type=\"submit\"\n            className=\"w-full\"\n            disabled={isGenerating || isLoading || !title.trim() || !abstract.trim()}\n          >\n            {isGenerating ? (\n              <>\n                <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\n                Generating...\n              </>\n            ) : (\n              <>\n                <Sparkles className=\"h-4 w-4 mr-2\" />\n                Generate Graphical Abstract\n              </>\n            )}\n          </Button>\n        </form>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;;;AARA;;;;;;;;AAeO,SAAS,iBAAiB,EAAE,UAAU,EAAyB;;IACpE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkD;IACnF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAEnC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,SAAS,IAAI,IAAI;YACrC;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,MAAM,WAAW;gBACf,OAAO,MAAM,IAAI;gBACjB,UAAU,SAAS,IAAI;gBACvB,OAAO,MAAM,IAAI,MAAM;gBACvB;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,YAAY,CAAA,OACV,KAAK,QAAQ,CAAC,WACV,KAAK,MAAM,CAAC,CAAA,IAAK,MAAM,WACvB;mBAAI;gBAAM;aAAQ;IAE1B;IAEA,MAAM,oBAAoB;QACxB;QAAa;QAAS;QAAO;QAAY;QAAU;QACnD;QAAU;QAAgB;QAAY;QAAc;QACpD;QAAW;QAAc;QAAc;KACxC;IAED,MAAM,SAAS;QACb;YAAE,OAAO;YAAU,OAAO;YAAU,aAAa;QAA6B;QAC9E;YAAE,OAAO;YAAY,OAAO;YAAY,aAAa;QAA+B;QACpF;YAAE,OAAO;YAAW,OAAO;YAAW,aAAa;QAAyB;QAC5E;YAAE,OAAO;YAAY,OAAO;YAAY,aAAa;QAA4B;KAClF;IAED,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;;kCACT,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAA0B;;;;;;;kCAGhD,6LAAC,mIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,6LAAC,mIAAA,CAAA,cAAW;0BACV,cAAA,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,SAAQ;oCAAQ,WAAU;8CAAsB;;;;;;8CAGvD,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAIZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,SAAQ;oCAAW,WAAU;8CAAsB;;;;;;8CAG1D,6LAAC,uIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC3C,aAAY;oCACZ,WAAU;oCACV,QAAQ;;;;;;;;;;;;sCAIZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,SAAQ;oCAAQ,WAAU;8CAAsB;;;;;;8CAGvD,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,aAAY;;;;;;;;;;;;sCAIhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;8CAAsB;;;;;;8CACvC,6LAAC;oCAAI,WAAU;8CACZ,OAAO,GAAG,CAAC,CAAC,4BACX,6LAAC;4CAEC,MAAK;4CACL,SAAS,IAAM,SAAS,YAAY,KAAK;4CACzC,WAAW,CAAC,kDAAkD,EAC5D,UAAU,YAAY,KAAK,GACvB,6CACA,yCACJ;;8DAEF,6LAAC;oDAAI,WAAU;8DAAe,YAAY,KAAK;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;8DAAyB,YAAY,WAAW;;;;;;;2CAV1D,YAAY,KAAK;;;;;;;;;;;;;;;;sCAgB9B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;8CAAsB;;;;;;8CAGvC,6LAAC;oCAAI,WAAU;8CACZ,kBAAkB,GAAG,CAAC,CAAC,wBACtB,6LAAC;4CAEC,MAAK;4CACL,SAAS,IAAM,oBAAoB;4CACnC,WAAW,CAAC,iDAAiD,EAC3D,SAAS,QAAQ,CAAC,WACd,2BACA,+CACJ;sDAED;2CATI;;;;;;;;;;;;;;;;sCAeb,6LAAC,qIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU,gBAAgB,aAAa,CAAC,MAAM,IAAI,MAAM,CAAC,SAAS,IAAI;sCAErE,6BACC;;kDACE,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA8B;;6DAInD;;kDACE,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AASrD;GA3KgB;;QAQQ,8HAAA,CAAA,iBAAc;;;KARtB", "debugId": null}}, {"offset": {"line": 2821, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/ubuntu/graphical_abstract/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { <PERSON><PERSON> } from '@/components/Header'\nimport { Toolbar } from '@/components/Toolbar'\nimport { Canvas } from '@/components/Canvas'\nimport { PropertiesPanel } from '@/components/PropertiesPanel'\nimport { AIGenerationForm } from '@/components/AIGenerationForm'\nimport { StockLibrary } from '@/components/StockLibrary'\nimport { Button } from '@/components/ui/button'\nimport { Sparkles, X, Image } from 'lucide-react'\nimport { AIGenerationRequest, CanvasElement, StockAsset } from '@/types'\nimport { useEditorStore } from '@/store/editorStore'\n\nexport default function Home() {\n  const [showAIForm, setShowAIForm] = useState(true)\n  const [showStockLibrary, setShowStockLibrary] = useState(false)\n  const { addElement, setLoading, setError } = useEditorStore()\n\n  const handleAIGeneration = async (request: AIGenerationRequest) => {\n    setLoading(true)\n    setError(null)\n\n    try {\n      // Simulate AI generation with mock data\n      await new Promise(resolve => setTimeout(resolve, 2000))\n\n      // Generate mock elements based on the request\n      const mockElements = generateMockElements(request)\n\n      // Add elements to canvas\n      mockElements.forEach(element => {\n        addElement(element)\n      })\n\n      setShowAIForm(false)\n    } catch (error) {\n      setError('Failed to generate graphical abstract. Please try again.')\n      console.error('AI Generation error:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const generateMockElements = (request: AIGenerationRequest): Omit<CanvasElement, 'id'>[] => {\n    const elements: Omit<CanvasElement, 'id'>[] = []\n\n    // Add title\n    elements.push({\n      type: 'text',\n      position: { x: 100, y: 50 },\n      size: { width: 800, height: 60 },\n      rotation: 0,\n      opacity: 1,\n      zIndex: 1,\n      properties: {\n        text: request.title,\n        fontSize: 24,\n        fontFamily: 'Arial',\n        fontWeight: 'bold',\n        color: '#1f2937',\n        textAlign: 'center'\n      }\n    })\n\n    // Add some shapes based on field\n    if (request.field.toLowerCase().includes('biology') || request.field.toLowerCase().includes('chemistry')) {\n      // Add molecule-like structures\n      elements.push({\n        type: 'shape',\n        position: { x: 150, y: 200 },\n        size: { width: 80, height: 80 },\n        rotation: 0,\n        opacity: 1,\n        zIndex: 2,\n        properties: {\n          fill: '#3b82f6',\n          borderRadius: 40\n        }\n      })\n\n      elements.push({\n        type: 'shape',\n        position: { x: 280, y: 250 },\n        size: { width: 60, height: 60 },\n        rotation: 0,\n        opacity: 1,\n        zIndex: 3,\n        properties: {\n          fill: '#ef4444',\n          borderRadius: 30\n        }\n      })\n\n      // Add connecting lines\n      elements.push({\n        type: 'line',\n        position: { x: 230, y: 240 },\n        size: { width: 50, height: 0 },\n        rotation: 0,\n        opacity: 1,\n        zIndex: 4,\n        properties: {\n          stroke: '#6b7280',\n          strokeWidth: 3\n        }\n      })\n    }\n\n    // Add process flow\n    elements.push({\n      type: 'arrow',\n      position: { x: 400, y: 300 },\n      size: { width: 150, height: 20 },\n      rotation: 0,\n      opacity: 1,\n      zIndex: 5,\n      properties: {\n        stroke: '#059669',\n        strokeWidth: 3,\n        arrowType: 'single'\n      }\n    })\n\n    // Add result box\n    elements.push({\n      type: 'shape',\n      position: { x: 600, y: 200 },\n      size: { width: 200, height: 120 },\n      rotation: 0,\n      opacity: 1,\n      zIndex: 6,\n      properties: {\n        fill: request.style === 'colorful' ? '#f59e0b' : '#e5e7eb',\n        stroke: '#374151',\n        strokeWidth: 2,\n        borderRadius: 8\n      }\n    })\n\n    elements.push({\n      type: 'text',\n      position: { x: 620, y: 240 },\n      size: { width: 160, height: 40 },\n      rotation: 0,\n      opacity: 1,\n      zIndex: 7,\n      properties: {\n        text: 'Results',\n        fontSize: 18,\n        fontFamily: 'Arial',\n        fontWeight: 'bold',\n        color: '#1f2937',\n        textAlign: 'center'\n      }\n    })\n\n    return elements\n  }\n\n  return (\n    <div className=\"h-screen flex flex-col bg-background\">\n      <Header />\n\n      <div className=\"flex-1 flex overflow-hidden\">\n        <Toolbar />\n\n        <div className=\"flex-1 relative\">\n          <Canvas />\n\n          {/* AI Generation Overlay */}\n          {showAIForm && (\n            <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center z-50\">\n              <div className=\"relative\">\n                <Button\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  onClick={() => setShowAIForm(false)}\n                  className=\"absolute -top-2 -right-2 z-10 bg-white hover:bg-gray-100\"\n                >\n                  <X className=\"h-4 w-4\" />\n                </Button>\n                <AIGenerationForm onGenerate={handleAIGeneration} />\n              </div>\n            </div>\n          )}\n\n          {/* Show AI Form Button */}\n          {!showAIForm && (\n            <Button\n              onClick={() => setShowAIForm(true)}\n              className=\"absolute top-4 left-4 z-10\"\n            >\n              <Sparkles className=\"h-4 w-4 mr-2\" />\n              AI Generate\n            </Button>\n          )}\n        </div>\n\n        <PropertiesPanel />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAEA;;;AAZA;;;;;;;;;;AAce,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAE1D,MAAM,qBAAqB,OAAO;QAChC,WAAW;QACX,SAAS;QAET,IAAI;YACF,wCAAwC;YACxC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,8CAA8C;YAC9C,MAAM,eAAe,qBAAqB;YAE1C,yBAAyB;YACzB,aAAa,OAAO,CAAC,CAAA;gBACnB,WAAW;YACb;YAEA,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,SAAS;YACT,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,WAAwC,EAAE;QAEhD,YAAY;QACZ,SAAS,IAAI,CAAC;YACZ,MAAM;YACN,UAAU;gBAAE,GAAG;gBAAK,GAAG;YAAG;YAC1B,MAAM;gBAAE,OAAO;gBAAK,QAAQ;YAAG;YAC/B,UAAU;YACV,SAAS;YACT,QAAQ;YACR,YAAY;gBACV,MAAM,QAAQ,KAAK;gBACnB,UAAU;gBACV,YAAY;gBACZ,YAAY;gBACZ,OAAO;gBACP,WAAW;YACb;QACF;QAEA,iCAAiC;QACjC,IAAI,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,cAAc,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,cAAc;YACxG,+BAA+B;YAC/B,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;gBAC3B,MAAM;oBAAE,OAAO;oBAAI,QAAQ;gBAAG;gBAC9B,UAAU;gBACV,SAAS;gBACT,QAAQ;gBACR,YAAY;oBACV,MAAM;oBACN,cAAc;gBAChB;YACF;YAEA,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;gBAC3B,MAAM;oBAAE,OAAO;oBAAI,QAAQ;gBAAG;gBAC9B,UAAU;gBACV,SAAS;gBACT,QAAQ;gBACR,YAAY;oBACV,MAAM;oBACN,cAAc;gBAChB;YACF;YAEA,uBAAuB;YACvB,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;gBAC3B,MAAM;oBAAE,OAAO;oBAAI,QAAQ;gBAAE;gBAC7B,UAAU;gBACV,SAAS;gBACT,QAAQ;gBACR,YAAY;oBACV,QAAQ;oBACR,aAAa;gBACf;YACF;QACF;QAEA,mBAAmB;QACnB,SAAS,IAAI,CAAC;YACZ,MAAM;YACN,UAAU;gBAAE,GAAG;gBAAK,GAAG;YAAI;YAC3B,MAAM;gBAAE,OAAO;gBAAK,QAAQ;YAAG;YAC/B,UAAU;YACV,SAAS;YACT,QAAQ;YACR,YAAY;gBACV,QAAQ;gBACR,aAAa;gBACb,WAAW;YACb;QACF;QAEA,iBAAiB;QACjB,SAAS,IAAI,CAAC;YACZ,MAAM;YACN,UAAU;gBAAE,GAAG;gBAAK,GAAG;YAAI;YAC3B,MAAM;gBAAE,OAAO;gBAAK,QAAQ;YAAI;YAChC,UAAU;YACV,SAAS;YACT,QAAQ;YACR,YAAY;gBACV,MAAM,QAAQ,KAAK,KAAK,aAAa,YAAY;gBACjD,QAAQ;gBACR,aAAa;gBACb,cAAc;YAChB;QACF;QAEA,SAAS,IAAI,CAAC;YACZ,MAAM;YACN,UAAU;gBAAE,GAAG;gBAAK,GAAG;YAAI;YAC3B,MAAM;gBAAE,OAAO;gBAAK,QAAQ;YAAG;YAC/B,UAAU;YACV,SAAS;YACT,QAAQ;YACR,YAAY;gBACV,MAAM;gBACN,UAAU;gBACV,YAAY;gBACZ,YAAY;gBACZ,OAAO;gBACP,WAAW;YACb;QACF;QAEA,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+HAAA,CAAA,SAAM;;;;;0BAEP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gIAAA,CAAA,UAAO;;;;;kCAER,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+HAAA,CAAA,SAAM;;;;;4BAGN,4BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,cAAc;4CAC7B,WAAU;sDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;sDAEf,6LAAC,yIAAA,CAAA,mBAAgB;4CAAC,YAAY;;;;;;;;;;;;;;;;;4BAMnC,CAAC,4BACA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,cAAc;gCAC7B,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAM3C,6LAAC,wIAAA,CAAA,kBAAe;;;;;;;;;;;;;;;;;AAIxB;GA7LwB;;QAGuB,8HAAA,CAAA,iBAAc;;;KAHrC", "debugId": null}}]}