'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/Header'
import { Toolbar } from '@/components/Toolbar'
import { Canvas } from '@/components/Canvas'
import { PropertiesPanel } from '@/components/PropertiesPanel'
import { AIGenerationForm } from '@/components/AIGenerationForm'
import { StockLibrary } from '@/components/StockLibrary'
import { Button } from '@/components/ui/button'
import { Sparkles, X, Image } from 'lucide-react'
import { AIGenerationRequest, CanvasElement, StockAsset } from '@/types'
import { useEditorStore } from '@/store/editorStore'

export default function Home() {
  const [showAIForm, setShowAIForm] = useState(true)
  const [showStockLibrary, setShowStockLibrary] = useState(false)
  const { addElement, setLoading, setError } = useEditorStore()

  const handleAIGeneration = async (request: AIGenerationRequest) => {
    setLoading(true)
    setError(null)

    try {
      // Simulate AI generation with mock data
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Generate mock elements based on the request
      const mockElements = generateMockElements(request)

      // Add elements to canvas
      mockElements.forEach(element => {
        addElement(element)
      })

      setShowAIForm(false)
    } catch (error) {
      setError('Failed to generate graphical abstract. Please try again.')
      console.error('AI Generation error:', error)
    } finally {
      setLoading(false)
    }
  }

  const generateMockElements = (request: AIGenerationRequest): Omit<CanvasElement, 'id'>[] => {
    const elements: Omit<CanvasElement, 'id'>[] = []

    // Add title
    elements.push({
      type: 'text',
      position: { x: 100, y: 50 },
      size: { width: 800, height: 60 },
      rotation: 0,
      opacity: 1,
      zIndex: 1,
      properties: {
        text: request.title,
        fontSize: 24,
        fontFamily: 'Arial',
        fontWeight: 'bold',
        color: '#1f2937',
        textAlign: 'center'
      }
    })

    // Add some shapes based on field
    if (request.field.toLowerCase().includes('biology') || request.field.toLowerCase().includes('chemistry')) {
      // Add molecule-like structures
      elements.push({
        type: 'shape',
        position: { x: 150, y: 200 },
        size: { width: 80, height: 80 },
        rotation: 0,
        opacity: 1,
        zIndex: 2,
        properties: {
          fill: '#3b82f6',
          borderRadius: 40
        }
      })

      elements.push({
        type: 'shape',
        position: { x: 280, y: 250 },
        size: { width: 60, height: 60 },
        rotation: 0,
        opacity: 1,
        zIndex: 3,
        properties: {
          fill: '#ef4444',
          borderRadius: 30
        }
      })

      // Add connecting lines
      elements.push({
        type: 'line',
        position: { x: 230, y: 240 },
        size: { width: 50, height: 0 },
        rotation: 0,
        opacity: 1,
        zIndex: 4,
        properties: {
          stroke: '#6b7280',
          strokeWidth: 3
        }
      })
    }

    // Add process flow
    elements.push({
      type: 'arrow',
      position: { x: 400, y: 300 },
      size: { width: 150, height: 20 },
      rotation: 0,
      opacity: 1,
      zIndex: 5,
      properties: {
        stroke: '#059669',
        strokeWidth: 3,
        arrowType: 'single'
      }
    })

    // Add result box
    elements.push({
      type: 'shape',
      position: { x: 600, y: 200 },
      size: { width: 200, height: 120 },
      rotation: 0,
      opacity: 1,
      zIndex: 6,
      properties: {
        fill: request.style === 'colorful' ? '#f59e0b' : '#e5e7eb',
        stroke: '#374151',
        strokeWidth: 2,
        borderRadius: 8
      }
    })

    elements.push({
      type: 'text',
      position: { x: 620, y: 240 },
      size: { width: 160, height: 40 },
      rotation: 0,
      opacity: 1,
      zIndex: 7,
      properties: {
        text: 'Results',
        fontSize: 18,
        fontFamily: 'Arial',
        fontWeight: 'bold',
        color: '#1f2937',
        textAlign: 'center'
      }
    })

    return elements
  }

  return (
    <div className="h-screen flex flex-col bg-background">
      <Header />

      <div className="flex-1 flex overflow-hidden">
        <Toolbar />

        <div className="flex-1 relative">
          <Canvas />

          {/* AI Generation Overlay */}
          {showAIForm && (
            <div className="absolute inset-0 bg-black/50 flex items-center justify-center z-50">
              <div className="relative">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowAIForm(false)}
                  className="absolute -top-2 -right-2 z-10 bg-white hover:bg-gray-100"
                >
                  <X className="h-4 w-4" />
                </Button>
                <AIGenerationForm onGenerate={handleAIGeneration} />
              </div>
            </div>
          )}

          {/* Show AI Form Button */}
          {!showAIForm && (
            <Button
              onClick={() => setShowAIForm(true)}
              className="absolute top-4 left-4 z-10"
            >
              <Sparkles className="h-4 w-4 mr-2" />
              AI Generate
            </Button>
          )}
        </div>

        <PropertiesPanel />
      </div>
    </div>
  )
}
