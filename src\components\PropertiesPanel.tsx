'use client'

import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON>lider } from '@/components/ui/slider'
import { 
  Trash2, 
  <PERSON><PERSON>, 
  <PERSON>ota<PERSON><PERSON>w, 
  Move3<PERSON>, 
  <PERSON><PERSON>,
  Type,
  Square
} from 'lucide-react'
import { useEditorStore } from '@/store/editorStore'
import { HexColorPicker } from 'react-colorful'

export function PropertiesPanel() {
  const {
    elements,
    selectedElementIds,
    updateElement,
    deleteElements,
    duplicateElements,
    bringToFront,
    sendToBack
  } = useEditorStore()

  const selectedElements = elements.filter(el => selectedElementIds.includes(el.id))
  const selectedElement = selectedElements.length === 1 ? selectedElements[0] : null

  if (selectedElementIds.length === 0) {
    return (
      <div className="w-80 bg-gray-50 border-l p-4">
        <div className="text-center text-gray-500 mt-8">
          <Square className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>Select an element to edit its properties</p>
        </div>
      </div>
    )
  }

  const handlePropertyChange = (property: string, value: any) => {
    selectedElementIds.forEach(id => {
      updateElement(id, {
        properties: {
          ...elements.find(el => el.id === id)?.properties,
          [property]: value
        }
      })
    })
  }

  const handlePositionChange = (axis: 'x' | 'y', value: number) => {
    selectedElementIds.forEach(id => {
      const element = elements.find(el => el.id === id)
      if (element) {
        updateElement(id, {
          position: {
            ...element.position,
            [axis]: value
          }
        })
      }
    })
  }

  const handleSizeChange = (dimension: 'width' | 'height', value: number) => {
    selectedElementIds.forEach(id => {
      const element = elements.find(el => el.id === id)
      if (element) {
        updateElement(id, {
          size: {
            ...element.size,
            [dimension]: value
          }
        })
      }
    })
  }

  const handleOpacityChange = (value: number[]) => {
    selectedElementIds.forEach(id => {
      updateElement(id, { opacity: value[0] / 100 })
    })
  }

  const handleRotationChange = (value: number[]) => {
    selectedElementIds.forEach(id => {
      updateElement(id, { rotation: value[0] })
    })
  }

  return (
    <div className="w-80 bg-gray-50 border-l overflow-y-auto">
      <div className="p-4 space-y-4">
        {/* Element Info */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <Move3D className="h-4 w-4" />
              {selectedElementIds.length === 1 
                ? `${selectedElement?.type} Element` 
                : `${selectedElementIds.length} Elements Selected`
              }
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {/* Actions */}
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => duplicateElements(selectedElementIds)}
                className="flex-1"
              >
                <Copy className="h-3 w-3 mr-1" />
                Copy
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => deleteElements(selectedElementIds)}
                className="flex-1"
              >
                <Trash2 className="h-3 w-3 mr-1" />
                Delete
              </Button>
            </div>

            {/* Layer Controls */}
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => selectedElementIds.forEach(bringToFront)}
                className="flex-1"
              >
                To Front
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => selectedElementIds.forEach(sendToBack)}
                className="flex-1"
              >
                To Back
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Position & Size */}
        {selectedElement && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Position & Size</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="text-xs text-gray-500">X</label>
                  <Input
                    type="number"
                    value={Math.round(selectedElement.position.x)}
                    onChange={(e) => handlePositionChange('x', Number(e.target.value))}
                    className="h-8"
                  />
                </div>
                <div>
                  <label className="text-xs text-gray-500">Y</label>
                  <Input
                    type="number"
                    value={Math.round(selectedElement.position.y)}
                    onChange={(e) => handlePositionChange('y', Number(e.target.value))}
                    className="h-8"
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="text-xs text-gray-500">Width</label>
                  <Input
                    type="number"
                    value={Math.round(selectedElement.size.width)}
                    onChange={(e) => handleSizeChange('width', Number(e.target.value))}
                    className="h-8"
                  />
                </div>
                <div>
                  <label className="text-xs text-gray-500">Height</label>
                  <Input
                    type="number"
                    value={Math.round(selectedElement.size.height)}
                    onChange={(e) => handleSizeChange('height', Number(e.target.value))}
                    className="h-8"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Transform */}
        {selectedElement && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <RotateCw className="h-4 w-4" />
                Transform
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-xs text-gray-500 mb-2 block">
                  Opacity: {Math.round(selectedElement.opacity * 100)}%
                </label>
                <Slider
                  value={[selectedElement.opacity * 100]}
                  onValueChange={handleOpacityChange}
                  max={100}
                  step={1}
                  className="w-full"
                />
              </div>
              <div>
                <label className="text-xs text-gray-500 mb-2 block">
                  Rotation: {Math.round(selectedElement.rotation)}°
                </label>
                <Slider
                  value={[selectedElement.rotation]}
                  onValueChange={handleRotationChange}
                  min={-180}
                  max={180}
                  step={1}
                  className="w-full"
                />
              </div>
            </CardContent>
          </Card>
        )}

        {/* Text Properties */}
        {selectedElement?.type === 'text' && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Type className="h-4 w-4" />
                Text Properties
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <label className="text-xs text-gray-500">Text</label>
                <Input
                  value={selectedElement.properties.text || ''}
                  onChange={(e) => handlePropertyChange('text', e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="text-xs text-gray-500">Font Size</label>
                  <Input
                    type="number"
                    value={selectedElement.properties.fontSize || 16}
                    onChange={(e) => handlePropertyChange('fontSize', Number(e.target.value))}
                    className="h-8"
                  />
                </div>
                <div>
                  <label className="text-xs text-gray-500">Font Family</label>
                  <select
                    value={selectedElement.properties.fontFamily || 'Arial'}
                    onChange={(e) => handlePropertyChange('fontFamily', e.target.value)}
                    className="h-8 w-full rounded border border-input bg-background px-2 text-sm"
                  >
                    <option value="Arial">Arial</option>
                    <option value="Helvetica">Helvetica</option>
                    <option value="Times New Roman">Times New Roman</option>
                    <option value="Georgia">Georgia</option>
                    <option value="Verdana">Verdana</option>
                  </select>
                </div>
              </div>
              <div>
                <label className="text-xs text-gray-500 mb-2 block">Text Color</label>
                <HexColorPicker
                  color={selectedElement.properties.color || '#000000'}
                  onChange={(color) => handlePropertyChange('color', color)}
                  style={{ width: '100%', height: '120px' }}
                />
              </div>
            </CardContent>
          </Card>
        )}

        {/* Shape Properties */}
        {selectedElement?.type === 'shape' && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Palette className="h-4 w-4" />
                Shape Properties
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <label className="text-xs text-gray-500 mb-2 block">Fill Color</label>
                <HexColorPicker
                  color={selectedElement.properties.fill || '#3b82f6'}
                  onChange={(color) => handlePropertyChange('fill', color)}
                  style={{ width: '100%', height: '120px' }}
                />
              </div>
              <div>
                <label className="text-xs text-gray-500">Border Radius</label>
                <Input
                  type="number"
                  value={selectedElement.properties.borderRadius || 0}
                  onChange={(e) => handlePropertyChange('borderRadius', Number(e.target.value))}
                  className="h-8"
                />
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
