'use client'

import React from 'react'
import { Button } from '@/components/ui/button'
import { 
  Download, 
  Save, 
  Undo, 
  Redo, 
  ZoomIn, 
  ZoomOut, 
  Share2,
  <PERSON><PERSON><PERSON>,
  User
} from 'lucide-react'
import { useEditorStore } from '@/store/editorStore'

export function Header() {
  const { 
    undo, 
    redo, 
    zoom, 
    setZoom, 
    history,
    isLoading 
  } = useEditorStore()

  const handleZoomIn = () => {
    setZoom(Math.min(zoom * 1.2, 5))
  }

  const handleZoomOut = () => {
    setZoom(Math.max(zoom / 1.2, 0.1))
  }

  const handleSave = () => {
    // TODO: Implement save functionality
    console.log('Save project')
  }

  const handleExport = () => {
    // TODO: Implement export functionality
    console.log('Export project')
  }

  const handleShare = () => {
    // TODO: Implement share functionality
    console.log('Share project')
  }

  return (
    <header className="h-16 border-b bg-background flex items-center justify-between px-4">
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">GA</span>
          </div>
          <h1 className="text-xl font-bold">Graphical Abstract Generator</h1>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        {/* Undo/Redo */}
        <Button
          variant="ghost"
          size="icon"
          onClick={undo}
          disabled={history.past.length === 0}
          title="Undo"
        >
          <Undo className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          onClick={redo}
          disabled={history.future.length === 0}
          title="Redo"
        >
          <Redo className="h-4 w-4" />
        </Button>

        <div className="w-px h-6 bg-border mx-2" />

        {/* Zoom Controls */}
        <Button
          variant="ghost"
          size="icon"
          onClick={handleZoomOut}
          title="Zoom Out"
        >
          <ZoomOut className="h-4 w-4" />
        </Button>
        <span className="text-sm font-medium min-w-[60px] text-center">
          {Math.round(zoom * 100)}%
        </span>
        <Button
          variant="ghost"
          size="icon"
          onClick={handleZoomIn}
          title="Zoom In"
        >
          <ZoomIn className="h-4 w-4" />
        </Button>

        <div className="w-px h-6 bg-border mx-2" />

        {/* Action Buttons */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleSave}
          disabled={isLoading}
        >
          <Save className="h-4 w-4 mr-2" />
          Save
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleExport}
          disabled={isLoading}
        >
          <Download className="h-4 w-4 mr-2" />
          Export
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleShare}
          disabled={isLoading}
        >
          <Share2 className="h-4 w-4 mr-2" />
          Share
        </Button>

        <div className="w-px h-6 bg-border mx-2" />

        {/* Settings and User */}
        <Button variant="ghost" size="icon" title="Settings">
          <Settings className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="icon" title="User Profile">
          <User className="h-4 w-4" />
        </Button>
      </div>
    </header>
  )
}
