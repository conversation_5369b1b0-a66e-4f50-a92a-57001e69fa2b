'use client'

import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Template } from '@/types'
import { useEditorStore } from '@/store/editorStore'

const mockTemplates: Template[] = [
  {
    id: '1',
    name: 'Biology Research',
    description: 'Perfect for biological research papers',
    category: 'Biology',
    thumbnail: '/api/placeholder/300/200',
    canvas: {
      width: 1200,
      height: 800,
      backgroundColor: '#ffffff'
    },
    elements: [
      {
        id: 'title',
        type: 'text',
        position: { x: 100, y: 50 },
        size: { width: 1000, height: 60 },
        rotation: 0,
        opacity: 1,
        zIndex: 1,
        properties: {
          text: 'Your Research Title Here',
          fontSize: 28,
          fontFamily: 'Arial',
          fontWeight: 'bold',
          color: '#1f2937',
          textAlign: 'center'
        }
      },
      {
        id: 'molecule1',
        type: 'shape',
        position: { x: 200, y: 200 },
        size: { width: 100, height: 100 },
        rotation: 0,
        opacity: 1,
        zIndex: 2,
        properties: {
          fill: '#3b82f6',
          borderRadius: 50
        }
      },
      {
        id: 'arrow1',
        type: 'arrow',
        position: { x: 350, y: 240 },
        size: { width: 200, height: 20 },
        rotation: 0,
        opacity: 1,
        zIndex: 3,
        properties: {
          stroke: '#059669',
          strokeWidth: 4,
          arrowType: 'single'
        }
      }
    ],
    tags: ['biology', 'research', 'molecules']
  },
  {
    id: '2',
    name: 'Data Analysis',
    description: 'Great for data science and analytics',
    category: 'Data Science',
    thumbnail: '/api/placeholder/300/200',
    canvas: {
      width: 1200,
      height: 800,
      backgroundColor: '#f8fafc'
    },
    elements: [
      {
        id: 'title',
        type: 'text',
        position: { x: 100, y: 50 },
        size: { width: 1000, height: 60 },
        rotation: 0,
        opacity: 1,
        zIndex: 1,
        properties: {
          text: 'Data Analysis Results',
          fontSize: 28,
          fontFamily: 'Arial',
          fontWeight: 'bold',
          color: '#1f2937',
          textAlign: 'center'
        }
      },
      {
        id: 'chart',
        type: 'shape',
        position: { x: 200, y: 200 },
        size: { width: 300, height: 200 },
        rotation: 0,
        opacity: 1,
        zIndex: 2,
        properties: {
          fill: '#e5e7eb',
          stroke: '#374151',
          strokeWidth: 2,
          borderRadius: 8
        }
      },
      {
        id: 'chart-label',
        type: 'text',
        position: { x: 220, y: 280 },
        size: { width: 260, height: 40 },
        rotation: 0,
        opacity: 1,
        zIndex: 3,
        properties: {
          text: 'Chart/Graph',
          fontSize: 18,
          fontFamily: 'Arial',
          color: '#374151',
          textAlign: 'center'
        }
      }
    ],
    tags: ['data', 'analysis', 'charts', 'graphs']
  },
  {
    id: '3',
    name: 'Process Flow',
    description: 'Ideal for showing processes and workflows',
    category: 'Process',
    thumbnail: '/api/placeholder/300/200',
    canvas: {
      width: 1200,
      height: 800,
      backgroundColor: '#ffffff'
    },
    elements: [
      {
        id: 'title',
        type: 'text',
        position: { x: 100, y: 50 },
        size: { width: 1000, height: 60 },
        rotation: 0,
        opacity: 1,
        zIndex: 1,
        properties: {
          text: 'Process Workflow',
          fontSize: 28,
          fontFamily: 'Arial',
          fontWeight: 'bold',
          color: '#1f2937',
          textAlign: 'center'
        }
      },
      {
        id: 'step1',
        type: 'shape',
        position: { x: 150, y: 200 },
        size: { width: 150, height: 100 },
        rotation: 0,
        opacity: 1,
        zIndex: 2,
        properties: {
          fill: '#dbeafe',
          stroke: '#3b82f6',
          strokeWidth: 2,
          borderRadius: 8
        }
      },
      {
        id: 'step1-text',
        type: 'text',
        position: { x: 170, y: 230 },
        size: { width: 110, height: 40 },
        rotation: 0,
        opacity: 1,
        zIndex: 3,
        properties: {
          text: 'Step 1',
          fontSize: 16,
          fontFamily: 'Arial',
          color: '#1f2937',
          textAlign: 'center'
        }
      },
      {
        id: 'arrow1',
        type: 'arrow',
        position: { x: 320, y: 240 },
        size: { width: 100, height: 20 },
        rotation: 0,
        opacity: 1,
        zIndex: 4,
        properties: {
          stroke: '#6b7280',
          strokeWidth: 3,
          arrowType: 'single'
        }
      },
      {
        id: 'step2',
        type: 'shape',
        position: { x: 450, y: 200 },
        size: { width: 150, height: 100 },
        rotation: 0,
        opacity: 1,
        zIndex: 5,
        properties: {
          fill: '#dcfce7',
          stroke: '#10b981',
          strokeWidth: 2,
          borderRadius: 8
        }
      },
      {
        id: 'step2-text',
        type: 'text',
        position: { x: 470, y: 230 },
        size: { width: 110, height: 40 },
        rotation: 0,
        opacity: 1,
        zIndex: 6,
        properties: {
          text: 'Step 2',
          fontSize: 16,
          fontFamily: 'Arial',
          color: '#1f2937',
          textAlign: 'center'
        }
      }
    ],
    tags: ['process', 'workflow', 'steps', 'flow']
  }
]

interface TemplateLibraryProps {
  onSelectTemplate: (template: Template) => void
  onClose: () => void
}

export function TemplateLibrary({ onSelectTemplate, onClose }: TemplateLibraryProps) {
  const categories = Array.from(new Set(mockTemplates.map(t => t.category)))

  return (
    <div className="w-full max-w-4xl max-h-[80vh] overflow-y-auto">
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold">Template Library</h2>
            <p className="text-gray-600">Choose a template to get started quickly</p>
          </div>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {mockTemplates.map((template) => (
            <Card key={template.id} className="cursor-pointer hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="w-full h-32 bg-gray-100 rounded-md mb-3 flex items-center justify-center">
                  <span className="text-gray-400 text-sm">Preview</span>
                </div>
                <CardTitle className="text-lg">{template.name}</CardTitle>
                <CardDescription>{template.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                    {template.category}
                  </span>
                  <Button 
                    size="sm" 
                    onClick={() => onSelectTemplate(template)}
                  >
                    Use Template
                  </Button>
                </div>
                <div className="flex flex-wrap gap-1 mt-2">
                  {template.tags.slice(0, 3).map((tag) => (
                    <span key={tag} className="text-xs text-gray-500 bg-gray-100 px-1 py-0.5 rounded">
                      {tag}
                    </span>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
