{"name": "graphical_abstract", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/supabase-js": "^2.50.0", "@types/fabric": "^5.3.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "fabric": "^6.7.0", "framer-motion": "^12.16.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.513.0", "next": "15.3.3", "openai": "^5.1.1", "react": "^19.0.0", "react-colorful": "^5.6.1", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "tailwind-merge": "^3.3.0", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}, "description": "This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).", "main": "index.js", "keywords": [], "author": "", "license": "ISC", "type": "module"}