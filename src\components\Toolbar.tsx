'use client'

import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { 
  MousePointer, 
  Type, 
  Square, 
  Circle, 
  ArrowRight, 
  Minus, 
  Image, 
  Shapes,
  Palette,
  Upload
} from 'lucide-react'
import { useEditorStore } from '@/store/editorStore'
import { CanvasElement } from '@/types'

export function Toolbar() {
  const { tool, setTool, addElement, elements } = useEditorStore()

  const tools = [
    { id: 'select', icon: MousePointer, label: 'Select' },
    { id: 'text', icon: Type, label: 'Text' },
    { id: 'shape', icon: Square, label: 'Rectangle' },
    { id: 'circle', icon: Circle, label: 'Circle' },
    { id: 'arrow', icon: ArrowRight, label: 'Arrow' },
    { id: 'line', icon: Minus, label: 'Line' },
    { id: 'image', icon: Image, label: 'Image' }
  ]

  const handleToolSelect = (toolId: string) => {
    setTool(toolId as any)
  }

  const handleAddShape = (shapeType: 'rectangle' | 'circle') => {
    const element: Omit<CanvasElement, 'id'> = {
      type: 'shape',
      position: { x: 100, y: 100 },
      size: { width: 100, height: 100 },
      rotation: 0,
      opacity: 1,
      zIndex: elements.length,
      properties: {
        fill: '#3b82f6',
        stroke: '',
        strokeWidth: 0,
        borderRadius: shapeType === 'circle' ? 50 : 0
      }
    }
    addElement(element)
  }

  const handleAddText = () => {
    const element: Omit<CanvasElement, 'id'> = {
      type: 'text',
      position: { x: 100, y: 100 },
      size: { width: 200, height: 40 },
      rotation: 0,
      opacity: 1,
      zIndex: elements.length,
      properties: {
        text: 'New Text',
        fontSize: 16,
        fontFamily: 'Arial',
        color: '#000000',
        textAlign: 'left'
      }
    }
    addElement(element)
  }

  const handleAddArrow = () => {
    const element: Omit<CanvasElement, 'id'> = {
      type: 'arrow',
      position: { x: 100, y: 100 },
      size: { width: 150, height: 20 },
      rotation: 0,
      opacity: 1,
      zIndex: elements.length,
      properties: {
        stroke: '#000000',
        strokeWidth: 2,
        arrowType: 'single'
      }
    }
    addElement(element)
  }

  const handleAddLine = () => {
    const element: Omit<CanvasElement, 'id'> = {
      type: 'line',
      position: { x: 100, y: 100 },
      size: { width: 150, height: 0 },
      rotation: 0,
      opacity: 1,
      zIndex: elements.length,
      properties: {
        stroke: '#000000',
        strokeWidth: 2
      }
    }
    addElement(element)
  }

  const handleImageUpload = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'image/*'
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (file) {
        const reader = new FileReader()
        reader.onload = (event) => {
          const element: Omit<CanvasElement, 'id'> = {
            type: 'image',
            position: { x: 100, y: 100 },
            size: { width: 200, height: 150 },
            rotation: 0,
            opacity: 1,
            zIndex: elements.length,
            properties: {
              src: event.target?.result as string,
              alt: file.name
            }
          }
          addElement(element)
        }
        reader.readAsDataURL(file)
      }
    }
    input.click()
  }

  return (
    <div className="w-16 bg-gray-50 border-r flex flex-col items-center py-4 space-y-2">
      {tools.map((toolItem) => (
        <Button
          key={toolItem.id}
          variant={tool === toolItem.id ? "default" : "ghost"}
          size="icon"
          onClick={() => {
            handleToolSelect(toolItem.id)
            
            // Auto-add elements for certain tools
            switch (toolItem.id) {
              case 'text':
                handleAddText()
                break
              case 'shape':
                handleAddShape('rectangle')
                break
              case 'circle':
                handleAddShape('circle')
                break
              case 'arrow':
                handleAddArrow()
                break
              case 'line':
                handleAddLine()
                break
              case 'image':
                handleImageUpload()
                break
            }
          }}
          title={toolItem.label}
          className="w-12 h-12"
        >
          <toolItem.icon className="h-5 w-5" />
        </Button>
      ))}

      <div className="w-8 h-px bg-gray-300 my-2" />

      {/* Quick Actions */}
      <Button
        variant="ghost"
        size="icon"
        onClick={() => handleAddShape('rectangle')}
        title="Add Rectangle"
        className="w-12 h-12"
      >
        <Square className="h-5 w-5" />
      </Button>

      <Button
        variant="ghost"
        size="icon"
        onClick={() => handleAddShape('circle')}
        title="Add Circle"
        className="w-12 h-12"
      >
        <Circle className="h-5 w-5" />
      </Button>

      <Button
        variant="ghost"
        size="icon"
        onClick={handleImageUpload}
        title="Upload Image"
        className="w-12 h-12"
      >
        <Upload className="h-5 w-5" />
      </Button>

      <div className="w-8 h-px bg-gray-300 my-2" />

      {/* Color Palette */}
      <div className="flex flex-col space-y-1">
        {['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#000000'].map((color) => (
          <button
            key={color}
            className="w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform"
            style={{ backgroundColor: color }}
            title={`Color: ${color}`}
            onClick={() => {
              // TODO: Apply color to selected elements
              console.log('Apply color:', color)
            }}
          />
        ))}
      </div>
    </div>
  )
}
