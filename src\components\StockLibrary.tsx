'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Search, Image, Shapes, Star } from 'lucide-react'
import { StockAsset } from '@/types'
import { useEditorStore } from '@/store/editorStore'

const mockAssets: StockAsset[] = [
  {
    id: '1',
    type: 'icon',
    name: 'DNA Helix',
    description: 'Double helix DNA structure',
    url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTMyIDhMMzIgNTYiIHN0cm9rZT0iIzMzODhGRiIgc3Ryb2tlLXdpZHRoPSIyIi8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMTYiIHI9IjQiIGZpbGw9IiNFRjQ0NDQiLz4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iNCIgZmlsbD0iIzEwQjk4MSIvPgo8Y2lyY2xlIGN4PSIzMiIgY3k9IjQ4IiByPSI0IiBmaWxsPSIjRjU5RTBCIi8+Cjwvc3ZnPgo=',
    thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTMyIDhMMzIgNTYiIHN0cm9rZT0iIzMzODhGRiIgc3Ryb2tlLXdpZHRoPSIyIi8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMTYiIHI9IjQiIGZpbGw9IiNFRjQ0NDQiLz4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iNCIgZmlsbD0iIzEwQjk4MSIvPgo8Y2lyY2xlIGN4PSIzMiIgY3k9IjQ4IiByPSI0IiBmaWxsPSIjRjU5RTBCIi8+Cjwvc3ZnPgo=',
    tags: ['dna', 'biology', 'genetics', 'helix'],
    category: 'Biology',
    license: 'Free'
  },
  {
    id: '2',
    type: 'icon',
    name: 'Molecule',
    description: 'Chemical molecule structure',
    url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxNiIgcj0iOCIgZmlsbD0iIzMzODhGRiIvPgo8Y2lyY2xlIGN4PSI0OCIgY3k9IjE2IiByPSI2IiBmaWxsPSIjRUY0NDQ0Ii8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iNDgiIHI9IjEwIiBmaWxsPSIjMTBCOTgxIi8+CjxsaW5lIHgxPSIyNCIgeTE9IjE2IiB4Mj0iNDAiIHkyPSIxNiIgc3Ryb2tlPSIjNkI3MjgwIiBzdHJva2Utd2lkdGg9IjIiLz4KPGxpbmUgeDE9IjI0IiB5MT0iMjQiIHgyPSIzMiIgeTI9IjQwIiBzdHJva2U9IiM2QjcyODAiIHN0cm9rZS13aWR0aD0iMiIvPgo8L3N2Zz4K',
    thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxNiIgcj0iOCIgZmlsbD0iIzMzODhGRiIvPgo8Y2lyY2xlIGN4PSI0OCIgY3k9IjE2IiByPSI2IiBmaWxsPSIjRUY0NDQ0Ii8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iNDgiIHI9IjEwIiBmaWxsPSIjMTBCOTgxIi8+CjxsaW5lIHgxPSIyNCIgeTE9IjE2IiB4Mj0iNDAiIHkyPSIxNiIgc3Ryb2tlPSIjNkI3MjgwIiBzdHJva2Utd2lkdGg9IjIiLz4KPGxpbmUgeDE9IjI0IiB5MT0iMjQiIHgyPSIzMiIgeTI9IjQwIiBzdHJva2U9IiM2QjcyODAiIHN0cm9rZS13aWR0aD0iMiIvPgo8L3N2Zz4K',
    tags: ['molecule', 'chemistry', 'atoms', 'bonds'],
    category: 'Chemistry',
    license: 'Free'
  },
  {
    id: '3',
    type: 'icon',
    name: 'Cell',
    description: 'Biological cell structure',
    url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMjgiIHN0cm9rZT0iIzMzODhGRiIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMzIiIHI9IjEyIiBmaWxsPSIjRUY0NDQ0Ii8+CjxjaXJjbGUgY3g9IjIwIiBjeT0iMjAiIHI9IjQiIGZpbGw9IiMxMEI5ODEiLz4KPGNpcmNsZSBjeD0iNDQiIGN5PSI0NCIgcj0iNCIgZmlsbD0iIzEwQjk4MSIvPgo8Y2lyY2xlIGN4PSI0NCIgY3k9IjIwIiByPSIzIiBmaWxsPSIjRjU5RTBCIi8+Cjwvc3ZnPgo=',
    thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMjgiIHN0cm9rZT0iIzMzODhGRiIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMzIiIHI9IjEyIiBmaWxsPSIjRUY0NDQ0Ii8+CjxjaXJjbGUgY3g9IjIwIiBjeT0iMjAiIHI9IjQiIGZpbGw9IiMxMEI5ODEiLz4KPGNpcmNsZSBjeD0iNDQiIGN5PSI0NCIgcj0iNCIgZmlsbD0iIzEwQjk4MSIvPgo8Y2lyY2xlIGN4PSI0NCIgY3k9IjIwIiByPSIzIiBmaWxsPSIjRjU5RTBCIi8+Cjwvc3ZnPgo=',
    tags: ['cell', 'biology', 'nucleus', 'organelles'],
    category: 'Biology',
    license: 'Free'
  },
  {
    id: '4',
    type: 'icon',
    name: 'Graph',
    description: 'Data visualization chart',
    url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3QgeD0iOCIgeT0iNDAiIHdpZHRoPSI4IiBoZWlnaHQ9IjE2IiBmaWxsPSIjMzM4OEZGIi8+CjxyZWN0IHg9IjIwIiB5PSIzMiIgd2lkdGg9IjgiIGhlaWdodD0iMjQiIGZpbGw9IiNFRjQ0NDQiLz4KPHJlY3QgeD0iMzIiIHk9IjI0IiB3aWR0aD0iOCIgaGVpZ2h0PSIzMiIgZmlsbD0iIzEwQjk4MSIvPgo8cmVjdCB4PSI0NCIgeT0iMTYiIHdpZHRoPSI4IiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjU5RTBCIi8+CjxsaW5lIHgxPSI4IiB5MT0iNTYiIHgyPSI1NiIgeTI9IjU2IiBzdHJva2U9IiM2QjcyODAiIHN0cm9rZS13aWR0aD0iMiIvPgo8bGluZSB4MT0iOCIgeTE9IjgiIHgyPSI4IiB5Mj0iNTYiIHN0cm9rZT0iIzZCNzI4MCIgc3Ryb2tlLXdpZHRoPSIyIi8+Cjwvc3ZnPgo=',
    thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3QgeD0iOCIgeT0iNDAiIHdpZHRoPSI4IiBoZWlnaHQ9IjE2IiBmaWxsPSIjMzM4OEZGIi8+CjxyZWN0IHg9IjIwIiB5PSIzMiIgd2lkdGg9IjgiIGhlaWdodD0iMjQiIGZpbGw9IiNFRjQ0NDQiLz4KPHJlY3QgeD0iMzIiIHk9IjI0IiB3aWR0aD0iOCIgaGVpZ2h0PSIzMiIgZmlsbD0iIzEwQjk4MSIvPgo8cmVjdCB4PSI0NCIgeT0iMTYiIHdpZHRoPSI4IiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjU5RTBCIi8+CjxsaW5lIHgxPSI4IiB5MT0iNTYiIHgyPSI1NiIgeTI9IjU2IiBzdHJva2U9IiM2QjcyODAiIHN0cm9rZS13aWR0aD0iMiIvPgo8bGluZSB4MT0iOCIgeTE9IjgiIHgyPSI4IiB5Mj0iNTYiIHN0cm9rZT0iIzZCNzI4MCIgc3Ryb2tlLXdpZHRoPSIyIi8+Cjwvc3ZnPgo=',
    tags: ['chart', 'graph', 'data', 'visualization'],
    category: 'Data',
    license: 'Free'
  },
  {
    id: '5',
    type: 'icon',
    name: 'Beaker',
    description: 'Laboratory beaker for chemistry',
    url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTI0IDhIMjRWMjRMMTYgNDhINDhMMzYgMjRWOEgzNiIgc3Ryb2tlPSIjMzM4OEZGIiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiLz4KPHBhdGggZD0iTTE2IDQ4SDQ4TDQwIDU2SDE2VjQ4WiIgZmlsbD0iIzEwQjk4MSIvPgo8Y2lyY2xlIGN4PSIzMiIgY3k9IjQwIiByPSIzIiBmaWxsPSIjRUY0NDQ0Ii8+CjxjaXJjbGUgY3g9IjI0IiBjeT0iNDQiIHI9IjIiIGZpbGw9IiNGNTlFMEIiLz4KPC9zdmc+Cg==',
    thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTI0IDhIMjRWMjRMMTYgNDhINDhMMzYgMjRWOEgzNiIgc3Ryb2tlPSIjMzM4OEZGIiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiLz4KPHBhdGggZD0iTTE2IDQ4SDQ4TDQwIDU2SDE2VjQ4WiIgZmlsbD0iIzEwQjk4MSIvPgo8Y2lyY2xlIGN4PSIzMiIgY3k9IjQwIiByPSIzIiBmaWxsPSIjRUY0NDQ0Ii8+CjxjaXJjbGUgY3g9IjI0IiBjeT0iNDQiIHI9IjIiIGZpbGw9IiNGNTlFMEIiLz4KPC9zdmc+Cg==',
    tags: ['beaker', 'chemistry', 'lab', 'experiment'],
    category: 'Chemistry',
    license: 'Free'
  },
  {
    id: '6',
    type: 'icon',
    name: 'Microscope',
    description: 'Scientific microscope',
    url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3QgeD0iMjQiIHk9IjgiIHdpZHRoPSIxNiIgaGVpZ2h0PSI0IiBmaWxsPSIjMzM4OEZGIi8+CjxyZWN0IHg9IjI4IiB5PSIxMiIgd2lkdGg9IjgiIGhlaWdodD0iMjAiIGZpbGw9IiM2QjcyODAiLz4KPGNpcmNsZSBjeD0iMzIiIGN5PSI0MCIgcj0iMTIiIHN0cm9rZT0iIzMzODhGRiIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iNDAiIHI9IjYiIGZpbGw9IiNFRjQ0NDQiLz4KPHJlY3QgeD0iMTYiIHk9IjU2IiB3aWR0aD0iMzIiIGhlaWdodD0iNCIgZmlsbD0iIzZCNzI4MCIvPgo8L3N2Zz4K',
    thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3QgeD0iMjQiIHk9IjgiIHdpZHRoPSIxNiIgaGVpZ2h0PSI0IiBmaWxsPSIjMzM4OEZGIi8+CjxyZWN0IHg9IjI4IiB5PSIxMiIgd2lkdGg9IjgiIGhlaWdodD0iMjAiIGZpbGw9IiM2QjcyODAiLz4KPGNpcmNsZSBjeD0iMzIiIGN5PSI0MCIgcj0iMTIiIHN0cm9rZT0iIzMzODhGRiIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iNDAiIHI9IjYiIGZpbGw9IiNFRjQ0NDQiLz4KPHJlY3QgeD0iMTYiIHk9IjU2IiB3aWR0aD0iMzIiIGhlaWdodD0iNCIgZmlsbD0iIzZCNzI4MCIvPgo8L3N2Zz4K',
    tags: ['microscope', 'science', 'research', 'lab'],
    category: 'Equipment',
    license: 'Free'
  }
]

interface StockLibraryProps {
  onSelectAsset: (asset: StockAsset) => void
  onClose: () => void
}

export function StockLibrary({ onSelectAsset, onClose }: StockLibraryProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('All')
  const { addElement, elements } = useEditorStore()

  const categories = ['All', ...Array.from(new Set(mockAssets.map(a => a.category)))]

  const filteredAssets = mockAssets.filter(asset => {
    const matchesSearch = asset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         asset.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesCategory = selectedCategory === 'All' || asset.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const handleAssetSelect = (asset: StockAsset) => {
    // Add the asset as an image element to the canvas
    const imageElement = {
      type: 'image' as const,
      position: { x: 100, y: 100 },
      size: { width: 100, height: 100 },
      rotation: 0,
      opacity: 1,
      zIndex: elements.length,
      properties: {
        src: asset.url,
        alt: asset.name
      }
    }
    
    addElement(imageElement)
    onSelectAsset(asset)
  }

  return (
    <div className="w-full max-w-4xl max-h-[80vh] overflow-y-auto bg-white rounded-lg shadow-lg">
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold flex items-center gap-2">
              <Shapes className="h-6 w-6" />
              Stock Library
            </h2>
            <p className="text-gray-600">Browse millions of icons, illustrations, and images</p>
          </div>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </div>

        {/* Search and Filters */}
        <div className="flex gap-4 mb-6">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search assets..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md bg-white"
          >
            {categories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>
        </div>

        {/* Assets Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {filteredAssets.map((asset) => (
            <Card key={asset.id} className="cursor-pointer hover:shadow-md transition-shadow">
              <CardContent className="p-3">
                <div className="aspect-square bg-gray-50 rounded-md mb-2 flex items-center justify-center overflow-hidden">
                  <img
                    src={asset.thumbnail}
                    alt={asset.name}
                    className="w-full h-full object-contain"
                  />
                </div>
                <h3 className="text-sm font-medium truncate">{asset.name}</h3>
                <p className="text-xs text-gray-500 mb-2">{asset.category}</p>
                <Button 
                  size="sm" 
                  className="w-full text-xs"
                  onClick={() => handleAssetSelect(asset)}
                >
                  Add to Canvas
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredAssets.length === 0 && (
          <div className="text-center py-12">
            <Image className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p className="text-gray-500">No assets found matching your search.</p>
          </div>
        )}
      </div>
    </div>
  )
}
