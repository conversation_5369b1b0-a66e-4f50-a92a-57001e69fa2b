import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { EditorState, CanvasElement, GraphicalAbstract } from '@/types'
import { generateId } from '@/lib/utils'

interface EditorStore extends EditorState {
  // Actions
  setCanvas: (canvas: Partial<GraphicalAbstract['canvas']>) => void
  addElement: (element: Omit<CanvasElement, 'id'>) => void
  updateElement: (id: string, updates: Partial<CanvasElement>) => void
  deleteElement: (id: string) => void
  deleteElements: (ids: string[]) => void
  selectElement: (id: string) => void
  selectElements: (ids: string[]) => void
  deselectAll: () => void
  duplicateElement: (id: string) => void
  duplicateElements: (ids: string[]) => void
  moveElement: (id: string, position: { x: number; y: number }) => void
  resizeElement: (id: string, size: { width: number; height: number }) => void
  rotateElement: (id: string, rotation: number) => void
  bringToFront: (id: string) => void
  sendToBack: (id: string) => void
  copyElements: (ids: string[]) => void
  pasteElements: () => void
  undo: () => void
  redo: () => void
  setZoom: (zoom: number) => void
  setPan: (pan: { x: number; y: number }) => void
  setTool: (tool: EditorState['tool']) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  reset: () => void
  loadProject: (project: GraphicalAbstract) => void
}

const initialState: EditorState = {
  canvas: {
    width: 1200,
    height: 800,
    backgroundColor: '#ffffff'
  },
  elements: [],
  selectedElementIds: [],
  clipboard: [],
  history: {
    past: [],
    present: [],
    future: []
  },
  zoom: 1,
  pan: { x: 0, y: 0 },
  tool: 'select',
  isLoading: false,
  error: null
}

export const useEditorStore = create<EditorStore>()(
  devtools(
    (set, get) => ({
      ...initialState,

      setCanvas: (canvas) =>
        set((state) => ({
          canvas: { ...state.canvas, ...canvas }
        })),

      addElement: (element) =>
        set((state) => {
          const newElement: CanvasElement = {
            ...element,
            id: generateId(),
            zIndex: Math.max(...state.elements.map(e => e.zIndex), 0) + 1
          }
          return {
            elements: [...state.elements, newElement],
            history: {
              past: [...state.history.past, state.history.present],
              present: [...state.elements, newElement],
              future: []
            }
          }
        }),

      updateElement: (id, updates) =>
        set((state) => {
          const updatedElements = state.elements.map(element =>
            element.id === id ? { ...element, ...updates } : element
          )
          return {
            elements: updatedElements,
            history: {
              past: [...state.history.past, state.history.present],
              present: updatedElements,
              future: []
            }
          }
        }),

      deleteElement: (id) =>
        set((state) => {
          const filteredElements = state.elements.filter(element => element.id !== id)
          return {
            elements: filteredElements,
            selectedElementIds: state.selectedElementIds.filter(selectedId => selectedId !== id),
            history: {
              past: [...state.history.past, state.history.present],
              present: filteredElements,
              future: []
            }
          }
        }),

      deleteElements: (ids) =>
        set((state) => {
          const filteredElements = state.elements.filter(element => !ids.includes(element.id))
          return {
            elements: filteredElements,
            selectedElementIds: state.selectedElementIds.filter(selectedId => !ids.includes(selectedId)),
            history: {
              past: [...state.history.past, state.history.present],
              present: filteredElements,
              future: []
            }
          }
        }),

      selectElement: (id) =>
        set({ selectedElementIds: [id] }),

      selectElements: (ids) =>
        set({ selectedElementIds: ids }),

      deselectAll: () =>
        set({ selectedElementIds: [] }),

      duplicateElement: (id) =>
        set((state) => {
          const element = state.elements.find(e => e.id === id)
          if (!element) return state

          const duplicatedElement: CanvasElement = {
            ...element,
            id: generateId(),
            position: {
              x: element.position.x + 20,
              y: element.position.y + 20
            },
            zIndex: Math.max(...state.elements.map(e => e.zIndex), 0) + 1
          }

          return {
            elements: [...state.elements, duplicatedElement],
            selectedElementIds: [duplicatedElement.id],
            history: {
              past: [...state.history.past, state.history.present],
              present: [...state.elements, duplicatedElement],
              future: []
            }
          }
        }),

      duplicateElements: (ids) =>
        set((state) => {
          const elementsToDuplicate = state.elements.filter(e => ids.includes(e.id))
          const duplicatedElements = elementsToDuplicate.map(element => ({
            ...element,
            id: generateId(),
            position: {
              x: element.position.x + 20,
              y: element.position.y + 20
            },
            zIndex: Math.max(...state.elements.map(e => e.zIndex), 0) + 1
          }))

          return {
            elements: [...state.elements, ...duplicatedElements],
            selectedElementIds: duplicatedElements.map(e => e.id),
            history: {
              past: [...state.history.past, state.history.present],
              present: [...state.elements, ...duplicatedElements],
              future: []
            }
          }
        }),

      moveElement: (id, position) =>
        get().updateElement(id, { position }),

      resizeElement: (id, size) =>
        get().updateElement(id, { size }),

      rotateElement: (id, rotation) =>
        get().updateElement(id, { rotation }),

      bringToFront: (id) =>
        set((state) => {
          const maxZIndex = Math.max(...state.elements.map(e => e.zIndex))
          return get().updateElement(id, { zIndex: maxZIndex + 1 })
        }),

      sendToBack: (id) =>
        set((state) => {
          const minZIndex = Math.min(...state.elements.map(e => e.zIndex))
          return get().updateElement(id, { zIndex: minZIndex - 1 })
        }),

      copyElements: (ids) =>
        set((state) => ({
          clipboard: state.elements.filter(e => ids.includes(e.id))
        })),

      pasteElements: () =>
        set((state) => {
          const pastedElements = state.clipboard.map(element => ({
            ...element,
            id: generateId(),
            position: {
              x: element.position.x + 20,
              y: element.position.y + 20
            },
            zIndex: Math.max(...state.elements.map(e => e.zIndex), 0) + 1
          }))

          return {
            elements: [...state.elements, ...pastedElements],
            selectedElementIds: pastedElements.map(e => e.id),
            history: {
              past: [...state.history.past, state.history.present],
              present: [...state.elements, ...pastedElements],
              future: []
            }
          }
        }),

      undo: () =>
        set((state) => {
          if (state.history.past.length === 0) return state
          const previous = state.history.past[state.history.past.length - 1]
          const newPast = state.history.past.slice(0, state.history.past.length - 1)
          return {
            elements: previous,
            history: {
              past: newPast,
              present: previous,
              future: [state.history.present, ...state.history.future]
            }
          }
        }),

      redo: () =>
        set((state) => {
          if (state.history.future.length === 0) return state
          const next = state.history.future[0]
          const newFuture = state.history.future.slice(1)
          return {
            elements: next,
            history: {
              past: [...state.history.past, state.history.present],
              present: next,
              future: newFuture
            }
          }
        }),

      setZoom: (zoom) => set({ zoom }),
      setPan: (pan) => set({ pan }),
      setTool: (tool) => set({ tool }),
      setLoading: (isLoading) => set({ isLoading }),
      setError: (error) => set({ error }),

      reset: () => set(initialState),

      loadProject: (project) =>
        set({
          canvas: project.canvas,
          elements: project.elements,
          selectedElementIds: [],
          history: {
            past: [],
            present: project.elements,
            future: []
          }
        })
    }),
    { name: 'editor-store' }
  )
)
