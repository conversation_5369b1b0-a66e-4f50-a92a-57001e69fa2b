'use client'

import React, { useEffect, useRef, useState } from 'react'
import * as fabric from 'fabric'
import { useEditorStore } from '@/store/editorStore'
import { CanvasElement } from '@/types'

export function Canvas() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const fabricCanvasRef = useRef<fabric.Canvas | null>(null)
  const [isInitialized, setIsInitialized] = useState(false)

  const {
    canvas: canvasConfig,
    elements,
    selectedElementIds,
    zoom,
    pan,
    tool,
    updateElement,
    selectElement,
    selectElements,
    deselectAll,
    moveElement,
    resizeElement,
    rotateElement
  } = useEditorStore()

  // Initialize Fabric.js canvas
  useEffect(() => {
    if (!canvasRef.current || isInitialized) return

    const fabricCanvas = new fabric.Canvas(canvasRef.current, {
      width: canvasConfig.width,
      height: canvasConfig.height,
      backgroundColor: canvasConfig.backgroundColor,
      selection: true,
      preserveObjectStacking: true
    })

    fabricCanvasRef.current = fabricCanvas
    setIsInitialized(true)

    // Canvas event handlers
    fabricCanvas.on('selection:created', (e) => {
      const selectedObjects = e.selected || []
      const ids = selectedObjects.map((obj: any) => obj.elementId).filter(Boolean)
      if (ids.length > 0) {
        selectElements(ids)
      }
    })

    fabricCanvas.on('selection:updated', (e) => {
      const selectedObjects = e.selected || []
      const ids = selectedObjects.map((obj: any) => obj.elementId).filter(Boolean)
      if (ids.length > 0) {
        selectElements(ids)
      }
    })

    fabricCanvas.on('selection:cleared', () => {
      deselectAll()
    })

    fabricCanvas.on('object:moving', (e) => {
      const obj = e.target as any
      if (obj.elementId) {
        moveElement(obj.elementId, { x: obj.left, y: obj.top })
      }
    })

    fabricCanvas.on('object:scaling', (e) => {
      const obj = e.target as any
      if (obj.elementId) {
        resizeElement(obj.elementId, {
          width: obj.width * obj.scaleX,
          height: obj.height * obj.scaleY
        })
      }
    })

    fabricCanvas.on('object:rotating', (e) => {
      const obj = e.target as any
      if (obj.elementId) {
        rotateElement(obj.elementId, obj.angle)
      }
    })

    // Cleanup
    return () => {
      fabricCanvas.dispose()
      fabricCanvasRef.current = null
      setIsInitialized(false)
    }
  }, [canvasRef.current])

  // Update canvas configuration
  useEffect(() => {
    if (!fabricCanvasRef.current) return

    fabricCanvasRef.current.setWidth(canvasConfig.width)
    fabricCanvasRef.current.setHeight(canvasConfig.height)
    fabricCanvasRef.current.setBackgroundColor(canvasConfig.backgroundColor, () => {
      fabricCanvasRef.current?.renderAll()
    })
  }, [canvasConfig])

  // Update zoom and pan
  useEffect(() => {
    if (!fabricCanvasRef.current) return

    fabricCanvasRef.current.setZoom(zoom)
    fabricCanvasRef.current.absolutePan(new fabric.Point(pan.x, pan.y))
  }, [zoom, pan])

  // Render elements on canvas
  useEffect(() => {
    if (!fabricCanvasRef.current) return

    const fabricCanvas = fabricCanvasRef.current

    // Clear existing objects
    fabricCanvas.clear()
    fabricCanvas.setBackgroundColor(canvasConfig.backgroundColor, () => {
      fabricCanvas.renderAll()
    })

    // Add elements to canvas
    elements.forEach((element) => {
      const fabricObject = createFabricObject(element)
      if (fabricObject) {
        fabricObject.elementId = element.id
        fabricCanvas.add(fabricObject)
      }
    })

    fabricCanvas.renderAll()
  }, [elements, canvasConfig.backgroundColor])

  // Handle tool changes
  useEffect(() => {
    if (!fabricCanvasRef.current) return

    const fabricCanvas = fabricCanvasRef.current

    switch (tool) {
      case 'select':
        fabricCanvas.selection = true
        fabricCanvas.defaultCursor = 'default'
        break
      case 'text':
        fabricCanvas.selection = false
        fabricCanvas.defaultCursor = 'text'
        break
      default:
        fabricCanvas.selection = false
        fabricCanvas.defaultCursor = 'crosshair'
        break
    }
  }, [tool])

  const createFabricObject = (element: CanvasElement): fabric.Object | null => {
    const commonProps = {
      left: element.position.x,
      top: element.position.y,
      width: element.size.width,
      height: element.size.height,
      angle: element.rotation,
      opacity: element.opacity,
      selectable: true,
      hasControls: true,
      hasBorders: true
    }

    switch (element.type) {
      case 'text':
        return new fabric.Text(element.properties.text || 'Text', {
          ...commonProps,
          fontSize: element.properties.fontSize || 16,
          fontFamily: element.properties.fontFamily || 'Arial',
          fontWeight: element.properties.fontWeight || 'normal',
          fill: element.properties.color || '#000000',
          textAlign: element.properties.textAlign || 'left'
        })

      case 'shape':
        return new fabric.Rect({
          ...commonProps,
          fill: element.properties.fill || '#3b82f6',
          stroke: element.properties.stroke || '',
          strokeWidth: element.properties.strokeWidth || 0,
          rx: element.properties.borderRadius || 0,
          ry: element.properties.borderRadius || 0
        })

      case 'image':
        if (element.properties.src) {
          return new Promise<fabric.Image>((resolve) => {
            fabric.Image.fromURL(element.properties.src!, (img) => {
              img.set(commonProps)
              resolve(img)
            })
          }) as any
        }
        return null

      case 'line':
        return new fabric.Line([
          element.position.x,
          element.position.y,
          element.position.x + element.size.width,
          element.position.y + element.size.height
        ], {
          stroke: element.properties.stroke || '#000000',
          strokeWidth: element.properties.strokeWidth || 2,
          selectable: true
        })

      case 'arrow':
        // Create arrow using a group of line and triangle
        const line = new fabric.Line([
          0, 0,
          element.size.width - 20, 0
        ], {
          stroke: element.properties.stroke || '#000000',
          strokeWidth: element.properties.strokeWidth || 2
        })

        const arrowHead = new fabric.Triangle({
          left: element.size.width - 20,
          top: -5,
          width: 10,
          height: 10,
          fill: element.properties.stroke || '#000000',
          angle: 90
        })

        return new fabric.Group([line, arrowHead], commonProps)

      default:
        return null
    }
  }

  const handleCanvasClick = (e: React.MouseEvent) => {
    if (!fabricCanvasRef.current) return

    const pointer = fabricCanvasRef.current.getPointer(e.nativeEvent)

    if (tool === 'text') {
      // Add text element
      const textElement: Omit<CanvasElement, 'id'> = {
        type: 'text',
        position: { x: pointer.x, y: pointer.y },
        size: { width: 100, height: 30 },
        rotation: 0,
        opacity: 1,
        zIndex: elements.length,
        properties: {
          text: 'New Text',
          fontSize: 16,
          fontFamily: 'Arial',
          color: '#000000'
        }
      }

      // This would be handled by the store
      // addElement(textElement)
    }
  }

  return (
    <div className="flex-1 overflow-hidden bg-gray-100 relative">
      <div className="absolute inset-0 flex items-center justify-center">
        <div
          className="bg-white shadow-lg"
          style={{
            transform: `scale(${zoom})`,
            transformOrigin: 'center center'
          }}
        >
          <canvas
            ref={canvasRef}
            onClick={handleCanvasClick}
            className="border border-gray-300"
          />
        </div>
      </div>
    </div>
  )
}
