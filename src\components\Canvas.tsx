'use client'

import React, { useEffect, useRef, useState } from 'react'
import { useEditorStore } from '@/store/editorStore'
import { CanvasElement } from '@/types'

export function Canvas() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isInitialized, setIsInitialized] = useState(false)

  const {
    canvas: canvasConfig,
    elements,
    selectedElementIds,
    zoom,
    pan,
    tool,
    updateElement,
    selectElement,
    selectElements,
    deselectAll,
    moveElement,
    resizeElement,
    rotateElement
  } = useEditorStore()

  // For now, let's create a simple HTML5 canvas implementation
  useEffect(() => {
    if (!canvasRef.current || isInitialized) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')

    if (!ctx) return

    canvas.width = canvasConfig.width
    canvas.height = canvasConfig.height

    setIsInitialized(true)

    // Cleanup
    return () => {
      setIsInitialized(false)
    }
  }, [canvasRef.current])

  // Update canvas configuration
  useEffect(() => {
    if (!canvasRef.current) return

    const canvas = canvasRef.current
    canvas.width = canvasConfig.width
    canvas.height = canvasConfig.height

    // Redraw canvas
    drawCanvas()
  }, [canvasConfig])

  // Render elements on canvas
  useEffect(() => {
    drawCanvas()
  }, [elements, canvasConfig.backgroundColor])

  const drawCanvas = () => {
    if (!canvasRef.current) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')

    if (!ctx) return

    // Clear canvas
    ctx.fillStyle = canvasConfig.backgroundColor
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    // Draw elements
    elements.forEach((element) => {
      drawElement(ctx, element)
    })
  }

  const drawElement = (ctx: CanvasRenderingContext2D, element: CanvasElement) => {
    ctx.save()

    // Apply transformations
    ctx.globalAlpha = element.opacity
    ctx.translate(element.position.x + element.size.width / 2, element.position.y + element.size.height / 2)
    ctx.rotate((element.rotation * Math.PI) / 180)
    ctx.translate(-element.size.width / 2, -element.size.height / 2)

    switch (element.type) {
      case 'text':
        ctx.fillStyle = element.properties.color || '#000000'
        ctx.font = `${element.properties.fontWeight || 'normal'} ${element.properties.fontSize || 16}px ${element.properties.fontFamily || 'Arial'}`
        ctx.textAlign = (element.properties.textAlign as CanvasTextAlign) || 'left'
        ctx.fillText(element.properties.text || '', 0, element.properties.fontSize || 16)
        break

      case 'shape':
        ctx.fillStyle = element.properties.fill || '#3b82f6'
        if (element.properties.borderRadius) {
          // Draw rounded rectangle
          const radius = element.properties.borderRadius
          ctx.beginPath()
          ctx.roundRect(0, 0, element.size.width, element.size.height, radius)
          ctx.fill()
        } else {
          ctx.fillRect(0, 0, element.size.width, element.size.height)
        }

        if (element.properties.stroke) {
          ctx.strokeStyle = element.properties.stroke
          ctx.lineWidth = element.properties.strokeWidth || 1
          ctx.strokeRect(0, 0, element.size.width, element.size.height)
        }
        break

      case 'line':
        ctx.strokeStyle = element.properties.stroke || '#000000'
        ctx.lineWidth = element.properties.strokeWidth || 2
        ctx.beginPath()
        ctx.moveTo(0, element.size.height / 2)
        ctx.lineTo(element.size.width, element.size.height / 2)
        ctx.stroke()
        break

      case 'arrow':
        ctx.strokeStyle = element.properties.stroke || '#000000'
        ctx.fillStyle = element.properties.stroke || '#000000'
        ctx.lineWidth = element.properties.strokeWidth || 2

        // Draw line
        ctx.beginPath()
        ctx.moveTo(0, element.size.height / 2)
        ctx.lineTo(element.size.width - 15, element.size.height / 2)
        ctx.stroke()

        // Draw arrowhead
        ctx.beginPath()
        ctx.moveTo(element.size.width, element.size.height / 2)
        ctx.lineTo(element.size.width - 15, element.size.height / 2 - 5)
        ctx.lineTo(element.size.width - 15, element.size.height / 2 + 5)
        ctx.closePath()
        ctx.fill()
        break
    }

    ctx.restore()
  }

  const handleCanvasClick = (e: React.MouseEvent) => {
    if (!canvasRef.current) return

    const rect = canvasRef.current.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    if (tool === 'text') {
      // Add text element
      const textElement: Omit<CanvasElement, 'id'> = {
        type: 'text',
        position: { x, y },
        size: { width: 100, height: 30 },
        rotation: 0,
        opacity: 1,
        zIndex: elements.length,
        properties: {
          text: 'New Text',
          fontSize: 16,
          fontFamily: 'Arial',
          color: '#000000'
        }
      }

      // This would be handled by the store
      // addElement(textElement)
    }
  }

  return (
    <div className="flex-1 overflow-hidden bg-gray-100 relative">
      <div className="absolute inset-0 flex items-center justify-center">
        <div
          className="bg-white shadow-lg"
          style={{
            transform: `scale(${zoom})`,
            transformOrigin: 'center center'
          }}
        >
          <canvas
            ref={canvasRef}
            onClick={handleCanvasClick}
            className="border border-gray-300 cursor-crosshair"
            style={{
              width: canvasConfig.width,
              height: canvasConfig.height
            }}
          />
        </div>
      </div>

      {/* Selection indicators */}
      {selectedElementIds.length > 0 && (
        <div className="absolute top-4 right-4 bg-blue-500 text-white px-3 py-1 rounded text-sm">
          {selectedElementIds.length} element(s) selected
        </div>
      )}
    </div>
  )
}
