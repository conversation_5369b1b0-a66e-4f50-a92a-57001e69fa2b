export interface GraphicalAbstract {
  id: string
  title: string
  description: string
  canvas: {
    width: number
    height: number
    backgroundColor: string
  }
  elements: CanvasElement[]
  createdAt: Date
  updatedAt: Date
}

export interface CanvasElement {
  id: string
  type: 'text' | 'shape' | 'image' | 'icon' | 'arrow' | 'line'
  position: {
    x: number
    y: number
  }
  size: {
    width: number
    height: number
  }
  rotation: number
  opacity: number
  zIndex: number
  properties: ElementProperties
}

export interface ElementProperties {
  // Text properties
  text?: string
  fontSize?: number
  fontFamily?: string
  fontWeight?: string
  color?: string
  textAlign?: 'left' | 'center' | 'right'
  
  // Shape properties
  fill?: string
  stroke?: string
  strokeWidth?: number
  borderRadius?: number
  
  // Image properties
  src?: string
  alt?: string
  
  // Arrow properties
  startPoint?: { x: number; y: number }
  endPoint?: { x: number; y: number }
  arrowType?: 'single' | 'double' | 'none'
  
  // Icon properties
  iconName?: string
  iconLibrary?: string
}

export interface Template {
  id: string
  name: string
  description: string
  category: string
  thumbnail: string
  canvas: GraphicalAbstract['canvas']
  elements: CanvasElement[]
  tags: string[]
}

export interface StockAsset {
  id: string
  type: 'icon' | 'illustration' | 'image'
  name: string
  description: string
  url: string
  thumbnail: string
  tags: string[]
  category: string
  license: string
}

export interface AIGenerationRequest {
  title: string
  abstract: string
  field: string
  style: 'modern' | 'academic' | 'minimal' | 'colorful'
  elements: string[]
}

export interface AIGenerationResponse {
  elements: CanvasElement[]
  suggestions: string[]
  layout: 'horizontal' | 'vertical' | 'grid' | 'flow'
}

export interface ExportOptions {
  format: 'png' | 'jpg' | 'pdf' | 'svg'
  quality: number
  width: number
  height: number
  dpi: number
  transparent: boolean
}

export interface EditorState {
  canvas: GraphicalAbstract['canvas']
  elements: CanvasElement[]
  selectedElementIds: string[]
  clipboard: CanvasElement[]
  history: {
    past: CanvasElement[][]
    present: CanvasElement[]
    future: CanvasElement[][]
  }
  zoom: number
  pan: { x: number; y: number }
  tool: 'select' | 'text' | 'shape' | 'arrow' | 'line' | 'image'
  isLoading: boolean
  error: string | null
}

export interface User {
  id: string
  email: string
  name: string
  avatar?: string
  subscription: 'free' | 'pro' | 'enterprise'
  createdAt: Date
}

export interface Project {
  id: string
  name: string
  description: string
  graphicalAbstract: GraphicalAbstract
  userId: string
  isPublic: boolean
  createdAt: Date
  updatedAt: Date
}
